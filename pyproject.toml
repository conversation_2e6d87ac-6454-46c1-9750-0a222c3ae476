[tool.poetry]
name = "transcription-api"
version = "0.1.0"
description = "A FastAPI application for transcription services"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
fastapi = ">=0.115.12,<0.116.0"
uvicorn = ">=0.34.2,<0.35.0"
pydantic = ">=2.11.4,<3.0.0"
deepgram-sdk = ">=4.1.0,<5.0.0"
python-dotenv = ">=1.1.0,<2.0.0"
pydantic-settings = ">=2.9.1,<3.0.0"
python-multipart = ">=0.0.20,<0.0.21"
openai = ">=1.12.0,<2.0.0"
sqlalchemy = ">=2.0.0,<3.0.0"
psycopg2-binary = ">=2.9.9,<3.0.0"
alembic = ">=1.12.0,<2.0.0"
passlib = { version = ">=1.7.4,<2.0.0", extras = ["bcrypt"] }
python-jose = { version = ">=3.4.0,<4.0.0", extras = ["cryptography"] }
email-validator = ">=2.1.0,<3.0.0"
pypdf2 = "^3.0.1"
python-docx = "^1.1.2"
requests = "^2.32.3"
apscheduler = "^3.11.0"
openpyxl = "^3.1.5"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
httpx = "^0.28.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
