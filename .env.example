# Deepgram API key
DEEPGRAM_API_KEY=YOUR_API_KEY

# OpenAI API key
OPENAI_API_KEY=YOUR_OPENAI_API_KEY

# Security settings
SECRET_KEY=09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7

# MCube API settings
MCUBE_API_URL=https://config.mcube.com/Restmcube-api/callsdatabydate
MCUBE_API_AUTH_TOKEN=YOUR_AUTH_TOKEN

# PostgreSQL Database settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=transcription
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

ADMIN_EMAIL = ""
ADMIN_PASSWORD = ""

# Email settings (Gmail SMTP)
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your-app-password

# Cron job settings
CRON_ENABLED=true
CRON_MAX_CONCURRENT=1
CRON_TIMEZONE=UTC