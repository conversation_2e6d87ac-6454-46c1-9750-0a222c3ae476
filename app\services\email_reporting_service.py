from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
import logging

from app.db.database import SessionLocal
from app.db.models import CallData, Transcription, SentimentAnalysisDB, EmailContact, InformationAccuracyDB
from app.models.email_reporting import (
    AgentEmailReport, AgentCallData, AgentSummaryStats,
    EmailReportingResult, EmailReportingConfig
)
from app.services.ai_email_service import generate_and_send_agent_email

logger = logging.getLogger(__name__)


class EmailReportingService:
    """Service for generating and sending personalized AI-powered email reports to agents."""

    def __init__(self):
        """Initialize the email reporting service."""
        pass

    async def generate_and_send_agent_reports(
        self,
        date_range_start: str,
        date_range_end: str,
        config: Optional[EmailReportingConfig] = None
    ) -> EmailReportingResult:
        """
        Generate and send email reports to all agents who made calls in the specified period.

        Args:
            date_range_start: Start date in YYYY-MM-DD format
            date_range_end: End date in YYYY-MM-DD format
            config: Optional configuration for report generation

        Returns:
            EmailReportingResult: Result of the operation
        """
        if config is None:
            config = EmailReportingConfig(
                date_range_start=date_range_start,
                date_range_end=date_range_end
            )

        result = EmailReportingResult(
            success=True,
            message="AI email reporting completed",
            reports_generated=0,
            emails_sent=0,
            errors=[],
            agent_results=[]
        )

        db = SessionLocal()

        try:
            # Get unique agents who made calls in the period
            agents = self._get_agents_with_calls(db, date_range_start, date_range_end)
            logger.info(f"Found {len(agents)} agents with calls in period {date_range_start} to {date_range_end}")

            if not agents:
                result.message = "No agents found with calls in the specified period"
                return result

            # Process each agent
            for agent_name in agents:
                try:
                    agent_result = await self._process_agent_report(
                        db, agent_name, date_range_start, date_range_end
                    )

                    result.agent_results.append(agent_result)

                    if agent_result.get("success", False):
                        result.reports_generated += 1
                        if agent_result.get("email_sent", False):
                            result.emails_sent += 1
                    else:
                        result.errors.append(agent_result.get("error", f"Unknown error for agent {agent_name}"))

                except Exception as e:
                    error_msg = f"Error processing agent {agent_name}: {str(e)}"
                    logger.error(error_msg)
                    result.errors.append(error_msg)
                    result.agent_results.append({
                        "agent_name": agent_name,
                        "success": False,
                        "error": error_msg
                    })

            # Update final result
            if result.errors:
                result.success = len(result.errors) < len(agents)  # Success if at least some agents processed
                result.message = f"Completed with {len(result.errors)} errors out of {len(agents)} agents"
            else:
                result.message = f"Successfully processed all {len(agents)} agents"

        except Exception as e:
            logger.error(f"Error in email reporting service: {str(e)}")
            result.success = False
            result.message = f"Email reporting failed: {str(e)}"
            result.errors.append(str(e))

        finally:
            db.close()

        return result

    def _get_agents_with_calls(self, db: Session, start_date: str, end_date: str) -> List[str]:
        """Get list of unique agent names who made calls in the specified period."""
        try:
            # Extract date part from datetime strings for comparison
            # start_date and end_date come in format "YYYY-MM-DD HH:MM:SS"
            # but call_date is stored as "YYYY-MM-DD"
            start_date_only = start_date.split(' ')[0] if ' ' in start_date else start_date
            end_date_only = end_date.split(' ')[0] if ' ' in end_date else end_date

            # Query for unique agent names in the date range
            agents = db.query(CallData.agent_name).filter(
                and_(
                    CallData.agent_name.isnot(None),
                    CallData.agent_name != "",
                    CallData.call_date >= start_date_only,
                    CallData.call_date <= end_date_only
                )
            ).distinct().all()

            return [agent[0] for agent in agents if agent[0]]

        except Exception as e:
            logger.error(f"Error getting agents with calls: {str(e)}")
            return []

    async def _process_agent_report(
        self,
        db: Session,
        agent_name: str,
        start_date: str,
        end_date: str
    ) -> Dict:
        """Process AI email generation and sending for a single agent."""
        agent_result = {
            "agent_name": agent_name,
            "success": False,
            "email_sent": False,
            "ai_email_generated": False,
            "error": None
        }

        try:
            # Get agent email address
            agent_email = self._get_agent_email(db, agent_name)
            if not agent_email:
                agent_result["error"] = f"No email address found for agent {agent_name}"
                logger.warning(agent_result["error"])
                return agent_result

            # Generate agent report data
            report = self._generate_agent_report_data(db, agent_name, start_date, end_date)
            if not report:
                agent_result["error"] = f"No call data found for agent {agent_name}"
                return agent_result

            report.agent_email = agent_email

            # Generate and send AI-powered personalized email using unified service
            email_result = generate_and_send_agent_email(report)

            # Update agent result with email processing results
            agent_result["success"] = email_result["success"]
            agent_result["email_sent"] = email_result["email_sent"]
            agent_result["ai_email_generated"] = email_result["ai_email_generated"]
            if email_result["error"]:
                agent_result["error"] = email_result["error"]

            if email_result["success"]:
                logger.info(f"Successfully processed AI email for agent {agent_name}")
            else:
                logger.error(f"Failed to process email for agent {agent_name}: {email_result.get('error', 'Unknown error')}")

        except Exception as e:
            agent_result["error"] = str(e)
            logger.error(f"Error processing agent {agent_name}: {str(e)}")

        return agent_result

    def _get_agent_email(self, db: Session, agent_name: str) -> Optional[str]:
        """Get email address for an agent from the EmailContact table."""
        try:
            contact = db.query(EmailContact).filter(
                EmailContact.name.ilike(f"%{agent_name}%")
            ).first()

            return contact.email if contact else None

        except Exception as e:
            logger.error(f"Error getting email for agent {agent_name}: {str(e)}")
            return None

    def _generate_agent_report_data(
        self,
        db: Session,
        agent_name: str,
        start_date: str,
        end_date: str
    ) -> Optional[AgentEmailReport]:
        """Generate complete report data for an agent."""
        try:
            # Extract date part from datetime strings for comparison
            # start_date and end_date come in format "YYYY-MM-DD HH:MM:SS"
            # but call_date is stored as "YYYY-MM-DD"
            start_date_only = start_date.split(' ')[0] if ' ' in start_date else start_date
            end_date_only = end_date.split(' ')[0] if ' ' in end_date else end_date

            # Get call data for the agent in the date range with information accuracy data
            calls_query = db.query(CallData).outerjoin(
                Transcription, CallData.transcription_id == Transcription.id
            ).outerjoin(
                SentimentAnalysisDB, Transcription.id == SentimentAnalysisDB.transcription_id
            ).outerjoin(
                InformationAccuracyDB, Transcription.id == InformationAccuracyDB.transcription_id
            ).filter(
                and_(
                    CallData.agent_name == agent_name,
                    CallData.call_date >= start_date_only,
                    CallData.call_date <= end_date_only
                )
            ).all()

            if not calls_query:
                return None

            # Process call details
            call_details = []
            sentiment_counts = {}
            total_duration = 0
            calls_with_transcription = 0
            calls_processed = 0

            for call_data in calls_query:
                # Get transcription, sentiment, and information accuracy data
                transcription = call_data.transcription
                sentiment_analysis = None
                information_accuracy = None

                if transcription:
                    calls_with_transcription += 1
                    sentiment_analysis = db.query(SentimentAnalysisDB).filter(
                        SentimentAnalysisDB.transcription_id == transcription.id
                    ).first()

                    information_accuracy = db.query(InformationAccuracyDB).filter(
                        InformationAccuracyDB.transcription_id == transcription.id
                    ).first()

                if call_data.processed:
                    calls_processed += 1

                # Format duration
                duration_seconds = call_data.call_duration or 0
                total_duration += duration_seconds
                duration_formatted = self._format_duration(duration_seconds)

                # Get sentiment info
                overall_sentiment = None
                key_points = []
                summary = None
                relevant_category = None

                if sentiment_analysis:
                    if sentiment_analysis.overall_sentiment:
                        sentiment_value = sentiment_analysis.overall_sentiment.value if hasattr(sentiment_analysis.overall_sentiment, 'value') else str(sentiment_analysis.overall_sentiment)
                        overall_sentiment = sentiment_value
                        sentiment_counts[sentiment_value] = sentiment_counts.get(sentiment_value, 0) + 1

                    key_points = sentiment_analysis.key_points or []
                    summary = sentiment_analysis.summary
                    relevant_category = sentiment_analysis.relevant_category

                # Get information accuracy data
                missed_information = []
                incorrect_information = []
                incomplete_information = []
                correct_information = []
                overall_assessment = None
                accuracy_percentage = None

                if information_accuracy:
                    missed_information = information_accuracy.missed_information or []
                    incorrect_information = information_accuracy.incorrect_information or []
                    incomplete_information = information_accuracy.incomplete_information or []
                    correct_information = information_accuracy.correct_information or []
                    overall_assessment = information_accuracy.overall_assessment
                    accuracy_percentage = information_accuracy.accuracy_percentage

                # Create call detail object with enhanced data including information accuracy
                call_detail = AgentCallData(
                    call_id=call_data.call_id,
                    call_date=call_data.call_date,
                    call_time=call_data.call_time,
                    duration_seconds=duration_seconds,
                    duration_formatted=duration_formatted,
                    student_name=call_data.student_name,
                    student_id=call_data.student_id,
                    call_direction=call_data.call_direction,
                    call_source=call_data.call_source,
                    has_transcription=transcription is not None,
                    overall_sentiment=overall_sentiment,
                    key_points=key_points,
                    summary=summary,
                    relevant_category=relevant_category,
                    created_at=call_data.created_at,
                    # Information accuracy fields
                    missed_information=missed_information,
                    incorrect_information=incorrect_information,
                    incomplete_information=incomplete_information,
                    correct_information=correct_information,
                    overall_assessment=overall_assessment,
                    accuracy_percentage=accuracy_percentage
                )

                call_details.append(call_detail)

            # Calculate summary statistics
            total_calls = len(call_details)
            average_duration = total_duration / total_calls if total_calls > 0 else 0
            most_common_sentiment = max(sentiment_counts, key=sentiment_counts.get) if sentiment_counts else None

            summary_stats = AgentSummaryStats(
                agent_name=agent_name,
                total_calls=total_calls,
                average_duration=average_duration,
                total_duration=total_duration,
                sentiment_breakdown=sentiment_counts,
                most_common_sentiment=most_common_sentiment,
                calls_with_transcription=calls_with_transcription,
                calls_processed=calls_processed
            )

            # Create complete report
            report = AgentEmailReport(
                agent_name=agent_name,
                report_period_start=start_date,
                report_period_end=end_date,
                summary_stats=summary_stats,
                call_details=call_details
            )

            return report

        except Exception as e:
            logger.error(f"Error generating report data for agent {agent_name}: {str(e)}")
            return None





    def _format_duration(self, seconds: int) -> str:
        """Format duration in seconds to human-readable format."""
        if seconds <= 0:
            return "0 minutes"

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        remaining_seconds = seconds % 60

        parts = []
        if hours > 0:
            parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
        if minutes > 0:
            parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
        if remaining_seconds > 0 and hours == 0:  # Only show seconds if less than an hour
            parts.append(f"{remaining_seconds} second{'s' if remaining_seconds != 1 else ''}")

        return " ".join(parts) if parts else "0 minutes"


