from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.models import User
from app.models.cron_job import (
    CronJobSystemStatus, ManualTriggerRequest, CronJobExecutionResponse
)
from app.services.scheduler_service import scheduler_service
from app.services.auth_service import get_current_active_user
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """
    Dependency to ensure the current user is an admin.

    Args:
        current_user: The authenticated user

    Returns:
        User: The admin user

    Raises:
        HTTPException: If user is not an admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


@router.get("/status", response_model=CronJobSystemStatus)
async def get_cron_job_status(
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get the current status of the cron job system (admin only).

    This endpoint returns information about the scheduler status, next run time,
    last execution details, and configuration settings.

    Args:
        current_user: The authenticated admin user

    Returns:
        CronJobStatus: Current status of the cron job system

    Raises:
        HTTPException: If user is not admin or error occurs
    """
    try:
        logger.info(f"Admin user {current_user.email} requesting cron job status")

        status = scheduler_service.get_scheduler_status()

        logger.info(f"Cron job status retrieved: enabled={status.enabled}, running={status.scheduler_running}")
        return status

    except Exception as e:
        logger.error(f"Error getting cron job status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving cron job status"
        )


@router.post("/trigger", response_model=CronJobExecutionResponse)
async def trigger_daily_call_processing(
    request: ManualTriggerRequest = ManualTriggerRequest(),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Manually trigger the daily call processing job (admin only).

    This endpoint allows admin users to manually execute the daily call processing
    job with optional date and concurrency overrides. This is useful for:
    - Processing missed days
    - Reprocessing specific dates
    - Testing the cron job functionality

    Args:
        request: ManualTriggerRequest with optional overrides
        current_user: The authenticated admin user

    Returns:
        CronJobExecutionResponse: Result of the job execution

    Raises:
        HTTPException: If user is not admin or execution fails
    """
    try:
        logger.info(f"Admin user {current_user.email} manually triggering daily call processing")

        if request.date_override:
            logger.info(f"Using date override: {request.date_override}")

        if request.max_concurrent:
            logger.info(f"Using max concurrent override: {request.max_concurrent}")

        # Execute the job
        result = await scheduler_service.trigger_daily_call_processing_manually(
            date_override=request.date_override,
            max_concurrent_override=request.max_concurrent
        )

        if result.success:
            logger.info(f"Manual trigger completed successfully: {result.message}")
        else:
            logger.error(f"Manual trigger failed: {result.message}")

        return result

    except ValueError as e:
        logger.error(f"Validation error in manual trigger: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error in manual trigger: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while executing the job"
        )


@router.post("/start")
async def start_scheduler(
    current_user: User = Depends(get_current_admin_user)
):
    """
    Start the cron job scheduler (admin only).

    This endpoint allows admin users to start the scheduler if it's not running.
    Note: The scheduler is automatically started when the application starts.

    Args:
        current_user: The authenticated admin user

    Returns:
        dict: Status message

    Raises:
        HTTPException: If user is not admin or operation fails
    """
    try:
        logger.info(f"Admin user {current_user.email} starting scheduler")

        if scheduler_service.is_running():
            return {"message": "Scheduler is already running"}

        scheduler_service.start_scheduler()

        logger.info("Scheduler started successfully by admin request")
        return {"message": "Scheduler started successfully"}

    except Exception as e:
        logger.error(f"Error starting scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start scheduler: {str(e)}"
        )


@router.post("/stop")
async def stop_scheduler(
    current_user: User = Depends(get_current_admin_user)
):
    """
    Stop the cron job scheduler (admin only).

    This endpoint allows admin users to stop the scheduler. Use with caution
    as this will prevent automatic daily processing until restarted.

    Args:
        current_user: The authenticated admin user

    Returns:
        dict: Status message

    Raises:
        HTTPException: If user is not admin or operation fails
    """
    try:
        logger.info(f"Admin user {current_user.email} stopping scheduler")

        if not scheduler_service.is_running():
            return {"message": "Scheduler is not running"}

        scheduler_service.stop_scheduler()

        logger.info("Scheduler stopped successfully by admin request")
        return {"message": "Scheduler stopped successfully"}

    except Exception as e:
        logger.error(f"Error stopping scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop scheduler: {str(e)}"
        )
