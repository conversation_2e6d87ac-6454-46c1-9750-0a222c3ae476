import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """Application settings."""

    ADMIN_EMAIL: str = "<EMAIL>"

    ADMIN_PASSWORD: str = "12345"

    # Deepgram API settings
    DEEPGRAM_API_KEY: str = os.getenv("DEEPGRAM_API_KEY", "YOUR_API_KEY")

    # OpenAI API settings (kept for backward compatibility)
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "YOUR_OPENAI_API_KEY")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4o")
    
    # Azure OpenAI API settings
    AZURE_OPENAI_API_KEY: str = os.getenv("AZURE_OPENAI_API_KEY", "YOUR_AZURE_API_KEY")
    AZURE_OPENAI_ENDPOINT: str = os.getenv("AZURE_OPENAI_ENDPOINT", "https://your-resource.cognitiveservices.azure.com/")
    AZURE_OPENAI_DEPLOYMENT: str = os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o")
    AZURE_OPENAI_API_VERSION: str = os.getenv("AZURE_OPENAI_API_VERSION", "2024-12-01-preview")
    
    # Toggle to use Azure OpenAI instead of OpenAI
    USE_AZURE_OPENAI: bool = os.getenv("USE_AZURE_OPENAI", "false").lower() == "true"



    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "")

    # MCube API settings
    MCUBE_API_URL: str = os.getenv("MCUBE_API_URL", "")
    MCUBE_API_AUTH_TOKEN: str = os.getenv("MCUBE_API_AUTH_TOKEN", "")

    # Database settings
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "transcription")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST", "localhost")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5432")
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"
    )

    print(DATABASE_URL)

    # Email settings
    SMTP_EMAIL: str = os.getenv("SMTP_EMAIL", "")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")

    # Cron job settings
    CRON_ENABLED: bool = os.getenv("CRON_ENABLED", "true").lower() == "true"
    CRON_MAX_CONCURRENT: int = int(os.getenv("CRON_MAX_CONCURRENT", "1"))
    CRON_TIMEZONE: str = os.getenv("CRON_TIMEZONE", "UTC")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8"
    }


settings = Settings()
