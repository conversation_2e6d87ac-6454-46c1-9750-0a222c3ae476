from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from app.core.config import settings
from app.api.transcription import router as transcription_router
from app.api.auth import router as auth_router
from app.api.school_info import router as school_info_router
from app.api.email import router as email_router
from app.api.email_contacts import router as email_contacts_router
from app.api.cron_jobs import router as cron_jobs_router
from app.api.email_reporting import router as email_reporting_router
from app.db.database import get_db
from app.services.auth_service import create_admin_user
from app.services.school_info_service import initialize_default_categories
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout), logging.FileHandler("app.log")],
)

# Set logger for your app modules
logger = logging.getLogger(__name__)

# Make sure other loggers work too
logging.getLogger("app").setLevel(logging.INFO)
logging.getLogger("uvicorn").setLevel(logging.INFO)


app = FastAPI(
    title="Transcription API",
    description="API for transcription services",
    version="0.1.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(transcription_router, prefix=f"{settings.API_V1_STR}/transcription", tags=["transcription"])
app.include_router(auth_router, prefix=f"{settings.API_V1_STR}/auth", tags=["authentication"])
app.include_router(school_info_router, prefix=f"{settings.API_V1_STR}/school-info", tags=["school-information"])
app.include_router(email_router, prefix=f"{settings.API_V1_STR}/email", tags=["email"])
app.include_router(email_contacts_router, prefix=f"{settings.API_V1_STR}/email-contacts", tags=["email-contacts"])
app.include_router(cron_jobs_router, prefix=f"{settings.API_V1_STR}/cron-jobs", tags=["cron-jobs"])
app.include_router(email_reporting_router, prefix=f"{settings.API_V1_STR}/email-reporting", tags=["email-reporting"])

# Create admin user and initialize school info on startup
@app.on_event("startup")
async def startup_event():
    db = next(get_db())
    create_admin_user(db)
    initialize_default_categories(db)

    # Start the cron job scheduler
    try:
        from app.services.scheduler_service import scheduler_service
        scheduler_service.start_scheduler()
        logger.info("Cron job scheduler started successfully")
    except Exception as e:
        logger.error(f"Failed to start cron job scheduler: {str(e)}")


# Shutdown event to clean up scheduler
@app.on_event("shutdown")
async def shutdown_event():
    try:
        from app.services.scheduler_service import scheduler_service
        scheduler_service.stop_scheduler()
        logger.info("Cron job scheduler stopped successfully")
    except Exception as e:
        logger.error(f"Error stopping cron job scheduler: {str(e)}")

@app.get("/")
async def root():
    return {"message": "Welcome to the Transcription API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
