import requests
import re
import os
from pathlib import Path
import tempfile
from typing import Optional

# For PDF extraction
try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

# For DOCX extraction
try:
    from docx import Document
except ImportError:
    Document = None

def extract_file_id_from_drive_url(drive_url: str) -> Optional[str]:
    """
    Extract Google Drive file ID from various Google Drive URL formats.
    
    Args:
        drive_url (str): Google Drive sharing URL
        
    Returns:
        str: File ID if found, None otherwise
    """
    patterns = [
        r'/file/d/([a-zA-Z0-9-_]+)',
        r'id=([a-zA-Z0-9-_]+)',
        r'/open\?id=([a-zA-Z0-9-_]+)',
        r'/document/d/([a-zA-Z0-9-_]+)',
        r'/presentation/d/([a-zA-Z0-9-_]+)',
        r'/spreadsheets/d/([a-zA-Z0-9-_]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, drive_url)
        if match:
            return match.group(1)
    
    return None

def download_from_google_drive(file_id: str, destination: str) -> bool:
    """
    Download a file from Google Drive using its file ID.
    
    Args:
        file_id (str): Google Drive file ID
        destination (str): Local path where file should be saved
        
    Returns:
        bool: True if download successful, False otherwise
    """
    # Google Drive direct download URL
    download_url = f"https://drive.google.com/uc?export=download&id={file_id}"
    
    try:
        session = requests.Session()
        response = session.get(download_url, stream=True)
        
        # Handle large files that require confirmation
        if 'download_warning' in response.cookies:
            params = {'id': file_id, 'confirm': response.cookies['download_warning']}
            response = session.get(download_url, params=params, stream=True)
        
        # Check if we got the actual file content
        if response.status_code == 200:
            with open(destination, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            return True
        else:
            print(f"Failed to download file. Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error downloading file: {str(e)}")
        return False

def extract_text_from_pdf(file_path: str) -> str:
    """
    Extract text from PDF file.
    
    Args:
        file_path (str): Path to PDF file
        
    Returns:
        str: Extracted text
    """
    if PyPDF2 is None:
        raise ImportError("PyPDF2 is required for PDF extraction. Install with: pip install PyPDF2")
    
    text = ""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}")
    
    return text.strip()

def extract_text_from_docx(file_path: str) -> str:
    """
    Extract text from DOCX file.
    
    Args:
        file_path (str): Path to DOCX file
        
    Returns:
        str: Extracted text
    """
    if Document is None:
        raise ImportError("python-docx is required for DOCX extraction. Install with: pip install python-docx")
    
    text = ""
    try:
        doc = Document(file_path)
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
    except Exception as e:
        print(f"Error extracting text from DOCX: {str(e)}")
    
    return text.strip()

def download_and_extract_text_from_drive(drive_url: str, keep_file: bool = False) -> Optional[str]:
    """
    Download a document from Google Drive and extract its text content.
    
    Args:
        drive_url (str): Google Drive sharing URL
        keep_file (bool): Whether to keep the downloaded file (default: False)
        
    Returns:
        str: Extracted text content, None if extraction failed
    """
    # Extract file ID from URL
    file_id = extract_file_id_from_drive_url(drive_url)
    if not file_id:
        print("Could not extract file ID from the provided URL")
        return None
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_file:
        temp_path = temp_file.name
    
    try:
        # Download the file
        print("Downloading file from Google Drive...")
        if not download_from_google_drive(file_id, temp_path):
            return None
        
        # Determine file type by trying to read the file header
        with open(temp_path, 'rb') as f:
            header = f.read(8)
        
        extracted_text = ""
        
        # Check if it's a PDF (starts with %PDF)
        if header.startswith(b'%PDF'):
            print("Detected PDF file, extracting text...")
            final_path = temp_path + '.pdf'
            os.rename(temp_path, final_path)
            extracted_text = extract_text_from_pdf(final_path)
            
        # Check if it's a DOCX (ZIP file starting with PK)
        elif header.startswith(b'PK'):
            print("Detected DOCX file, extracting text...")
            final_path = temp_path + '.docx'
            os.rename(temp_path, final_path)
            extracted_text = extract_text_from_docx(final_path)
            
        else:
            print("Unknown file format. Supported formats: PDF, DOCX")
            return None
        
        # Optionally keep the file
        if keep_file:
            filename = f"downloaded_file_{file_id}{Path(final_path).suffix}"
            os.rename(final_path, filename)
            print(f"File saved as: {filename}")
        else:
            # Clean up temporary file
            if os.path.exists(final_path):
                os.remove(final_path)
        
        return extracted_text
        
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        # Clean up temporary files
        for path in [temp_path, temp_path + '.pdf', temp_path + '.docx']:
            if os.path.exists(path):
                os.remove(path)
        return None
    
    finally:
        # Clean up original temp file if it still exists
        if os.path.exists(temp_path):
            os.remove(temp_path)
