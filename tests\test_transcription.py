import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from app.main import app
import io

client = TestClient(app)


@pytest.fixture
def mock_wav_file():
    """Create a mock WAV file for testing."""
    return io.BytesIO(b"mock audio content")


@patch("app.services.transcription_service.transcribe_audio_file")
def test_transcribe_endpoint(mock_transcribe, mock_wav_file):
    """Test the transcription endpoint with a mock WAV file."""
    # Configure the mock to return a successful transcription
    mock_transcribe.return_value = AsyncMock(return_value={
        "text": "This is a test transcription.",
        "language": "en",
        "metadata": {
            "duration_seconds": 30.5
        }
    })
    
    # Create a test file
    files = {"file": ("test.wav", mock_wav_file, "audio/wav")}
    
    # Make the request
    response = client.post(
        "/api/v1/transcription/transcribe",
        files=files,
        data={
            "language": "en",
            "smart_format": "true",
            "punctuate": "true",
            "paragraphs": "true",
            "diarize": "false",
            "profanity_filter": "false"
        }
    )
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "id" in data
    assert data["text"] == "This is a test transcription."
    assert data["language"] == "en"
    assert data["duration_seconds"] == 30.5
    
    # Verify the mock was called with the correct parameters
    mock_transcribe.assert_called_once()
    call_args = mock_transcribe.call_args[1]
    assert "file_content" in call_args
    assert call_args["language"] == "en"
    assert call_args["smart_format"] is True
    assert call_args["punctuate"] is True
    assert call_args["paragraphs"] is True
    assert call_args["diarize"] is False
    assert call_args["profanity_filter"] is False
