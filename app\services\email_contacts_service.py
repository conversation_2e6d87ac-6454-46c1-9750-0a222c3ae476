from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.db.models import EmailContact
from app.models.email_contacts import (
    BulkUpsertRequest, EmailContactRequest, EmailContactResponse, 
    EmailContactUpdate, BulkUpsertResponse, EmailContactList, DeleteResponse
)
from typing import List, Optional, <PERSON>ple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def get_email_by_name(db: Session, name: str) -> Optional[str]:
    """
    Get email address by contact name.
    
    Args:
        db: Database session
        name: Contact name to search for
        
    Returns:
        Optional[str]: Email address if contact exists, None otherwise
    """
    try:
        contact = db.query(EmailContact).filter(EmailContact.name == name.strip()).first()
        return contact.email if contact else None
    except Exception as e:
        logger.error(f"Error getting email by name '{name}': {str(e)}")
        return None


def get_all_email_contacts(db: Session) -> EmailContactList:
    """
    Get all email contacts.
    
    Args:
        db: Database session
        
    Returns:
        EmailContactList: List of all email contacts
    """
    try:
        contacts = db.query(EmailContact).order_by(EmailContact.name).all()
        
        contact_responses = [
            EmailContactResponse(
                id=contact.id,
                name=contact.name,
                email=contact.email,
                created_at=contact.created_at,
                updated_at=contact.updated_at
            )
            for contact in contacts
        ]
        
        return EmailContactList(
            contacts=contact_responses,
            total_count=len(contact_responses)
        )
        
    except Exception as e:
        logger.error(f"Error getting all email contacts: {str(e)}")
        raise


def get_email_contact_by_name(db: Session, name: str) -> Optional[EmailContactResponse]:
    """
    Get a specific email contact by name.
    
    Args:
        db: Database session
        name: Contact name
        
    Returns:
        Optional[EmailContactResponse]: Contact if found, None otherwise
    """
    try:
        contact = db.query(EmailContact).filter(EmailContact.name == name.strip()).first()
        
        if not contact:
            return None
            
        return EmailContactResponse(
            id=contact.id,
            name=contact.name,
            email=contact.email,
            created_at=contact.created_at,
            updated_at=contact.updated_at
        )
        
    except Exception as e:
        logger.error(f"Error getting email contact by name '{name}': {str(e)}")
        raise


def bulk_upsert_email_contacts(db: Session, request: BulkUpsertRequest) -> BulkUpsertResponse:
    """
    Bulk create or update email contacts.
    
    Args:
        db: Database session
        request: BulkUpsertRequest containing name-email pairs
        
    Returns:
        BulkUpsertResponse: Response with operation results
    """
    created_count = 0
    updated_count = 0
    processed_contacts = []
    
    try:
        for name, email in request.contacts.items():
            name = name.strip()
            
            # Check if contact already exists
            existing_contact = db.query(EmailContact).filter(EmailContact.name == name).first()
            
            if existing_contact:
                # Update existing contact
                existing_contact.email = str(email)
                existing_contact.updated_at = datetime.now()
                updated_count += 1
                
                processed_contacts.append(EmailContactResponse(
                    id=existing_contact.id,
                    name=existing_contact.name,
                    email=existing_contact.email,
                    created_at=existing_contact.created_at,
                    updated_at=existing_contact.updated_at
                ))
                
                logger.info(f"Updated email contact: {name} -> {email}")
                
            else:
                # Create new contact
                new_contact = EmailContact(
                    name=name,
                    email=str(email)
                )
                
                db.add(new_contact)
                db.flush()  # Flush to get the ID
                created_count += 1
                
                processed_contacts.append(EmailContactResponse(
                    id=new_contact.id,
                    name=new_contact.name,
                    email=new_contact.email,
                    created_at=new_contact.created_at,
                    updated_at=new_contact.updated_at
                ))
                
                logger.info(f"Created new email contact: {name} -> {email}")
        
        db.commit()
        
        total_processed = created_count + updated_count
        
        return BulkUpsertResponse(
            success=True,
            message=f"Successfully processed {total_processed} contacts ({created_count} created, {updated_count} updated)",
            created_count=created_count,
            updated_count=updated_count,
            total_processed=total_processed,
            contacts=processed_contacts
        )
        
    except IntegrityError as e:
        db.rollback()
        logger.error(f"Integrity error during bulk upsert: {str(e)}")
        raise ValueError("Database integrity error. This might be due to duplicate names or invalid data.")
    except Exception as e:
        db.rollback()
        logger.error(f"Error during bulk upsert: {str(e)}")
        raise


def update_email_contact(db: Session, name: str, update_data: EmailContactUpdate) -> Optional[EmailContactResponse]:
    """
    Update a specific email contact.
    
    Args:
        db: Database session
        name: Contact name to update
        update_data: EmailContactUpdate with new email
        
    Returns:
        Optional[EmailContactResponse]: Updated contact if found, None otherwise
    """
    try:
        contact = db.query(EmailContact).filter(EmailContact.name == name.strip()).first()
        
        if not contact:
            return None
        
        contact.email = str(update_data.email)
        contact.updated_at = datetime.now()
        
        db.commit()
        db.refresh(contact)
        
        logger.info(f"Updated email contact: {name} -> {update_data.email}")
        
        return EmailContactResponse(
            id=contact.id,
            name=contact.name,
            email=contact.email,
            created_at=contact.created_at,
            updated_at=contact.updated_at
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating email contact '{name}': {str(e)}")
        raise


def delete_email_contact(db: Session, name: str) -> Optional[DeleteResponse]:
    """
    Delete a specific email contact.
    
    Args:
        db: Database session
        name: Contact name to delete
        
    Returns:
        Optional[DeleteResponse]: Delete response if contact found, None otherwise
    """
    try:
        contact = db.query(EmailContact).filter(EmailContact.name == name.strip()).first()
        
        if not contact:
            return None
        
        db.delete(contact)
        db.commit()
        
        logger.info(f"Deleted email contact: {name}")
        
        return DeleteResponse(
            success=True,
            message=f"Contact '{name}' deleted successfully",
            deleted_contact=name
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting email contact '{name}': {str(e)}")
        raise
