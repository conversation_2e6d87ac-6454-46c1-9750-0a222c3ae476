# Transcription API

A FastAPI application for audio transcription services using Deepgram.

## Features

- RESTful API for audio transcription
- Built with FastAPI and Poetry
- Integration with Deepgram for high-quality audio transcription
- Support for multiple languages
- Configurable transcription options (smart formatting, punctuation, paragraphs, etc.)
- File upload for audio files (WAV format recommended)

## Requirements

- Python 3.8+
- Poetry for dependency management

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd transcription
   ```

2. Install dependencies with Poetry:
   ```
   poetry install
   ```

3. Set up environment variables:
   ```
   cp .env.example .env
   ```

   Then edit the `.env` file to add your Deepgram API key:
   ```
   DEEPGRAM_API_KEY=your_deepgram_api_key
   ```

## Usage

### Running the application

```bash
# Activate the Poetry virtual environment
poetry shell

# Run the application with Uvicorn
uvicorn app.main:app --reload
```

The API will be available at http://localhost:8000

### API Documentation

Once the application is running, you can access the interactive API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Using the Transcription API

You can use the transcription API to transcribe audio files:

```bash
# Using curl to upload a WAV file for transcription
curl -X POST http://localhost:8000/api/v1/transcription/transcribe \
  -F "file=@/path/to/your/audio.wav" \
  -F "language=en" \
  -F "smart_format=true" \
  -F "punctuate=true" \
  -F "paragraphs=true" \
  -F "diarize=false" \
  -F "profanity_filter=false"
```

Or using Python with the `requests` library:

```python
import requests

url = "http://localhost:8000/api/v1/transcription/transcribe"

# Parameters
files = {"file": open("audio.wav", "rb")}
data = {
    "language": "en",
    "smart_format": "true",
    "punctuate": "true",
    "paragraphs": "true",
    "diarize": "false",
    "profanity_filter": "false"
}

response = requests.post(url, files=files, data=data)
print(response.json())
```

## Development

### Running tests

```bash
poetry run pytest
```

### Project Structure

```
transcription/
├── app/
│   ├── api/            # API endpoints
│   ├── core/           # Core functionality, config
│   ├── models/         # Pydantic models
│   ├── services/       # Business logic
│   └── main.py         # Application entry point
├── tests/              # Test suite
├── pyproject.toml      # Poetry configuration
└── README.md           # This file
```

## License

[MIT](LICENSE)
