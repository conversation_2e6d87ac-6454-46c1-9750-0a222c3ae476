
# Production stage
FROM python:3.11-slim as production

# Set work directory
WORKDIR /app


# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.8.5 \
    POETRY_HOME=/opt/poetry \
    POETRY_VIRTUALENVS_IN_PROJECT=false

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean


RUN pip install --no-cache-dir "poetry==$POETRY_VERSION"

COPY pyproject.toml poetry.lock ./

RUN poetry install --no-root --no-dev --no-root

# Copy application code
COPY . .

# Expose port
EXPOSE 8000


# Default command
CMD poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000
