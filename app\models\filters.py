from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from enum import Enum
from datetime import datetime, date


class FilterType(str, Enum):
    """Enum for date filter types."""
    TODAY = "today"
    YESTERDAY = "yesterday"
    SPECIFIC_DAY = "specific_day"
    DATE_RANGE = "date_range"
    ALL = "all"


class SentimentFilter(str, Enum):
    """Enum for sentiment filtering."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    VERY_POSITIVE = "very_positive"
    VERY_NEGATIVE = "very_negative"


class ConversationFlagFilter(str, Enum):
    """Enum for conversation flag filtering."""
    FRUSTRATION = "frustration"
    CONFUSION = "confusion"
    URGENCY = "urgency"
    SATISFACTION = "satisfaction"
    ABUSIVE = "abusive"


class CallFilters(BaseModel):
    """Model for call filtering parameters."""

    # Date filtering parameters
    filter_type: FilterType = Field(
        FilterType.ALL,
        description="Type of date filtering to apply"
    )
    specific_date: Optional[date] = Field(
        None,
        description="Specific date for filtering (YYYY-MM-DD) - required when filter_type='specific_day'"
    )
    start_date: Optional[date] = Field(
        None,
        description="Start date for range filtering (YYYY-MM-DD) - required when filter_type='date_range'"
    )
    end_date: Optional[date] = Field(
        None,
        description="End date for range filtering (YYYY-MM-DD) - required when filter_type='date_range'"
    )

    # Sentiment filtering parameters
    sentiment_filter: Optional[List[SentimentFilter]] = Field(
        None,
        description="Filter by overall sentiment values"
    )

    # Conversation flag filtering parameters
    conversation_flags: Optional[List[ConversationFlagFilter]] = Field(
        None,
        description="Filter by emotional flags (frustration, confusion, urgency, satisfaction, abusive)"
    )

    # Agent name filtering parameter
    agent_name: Optional[str] = Field(
        None,
        description="Filter by agent name (case-insensitive partial matching)"
    )

    @validator('specific_date')
    def validate_specific_date(cls, v, values):
        """Validate that specific_date is provided when filter_type is 'specific_day'."""
        if values.get('filter_type') == FilterType.SPECIFIC_DAY and v is None:
            raise ValueError("specific_date is required when filter_type is 'specific_day'")
        return v

    @validator('start_date')
    def validate_start_date(cls, v, values):
        """Validate that start_date is provided when filter_type is 'date_range'."""
        if values.get('filter_type') == FilterType.DATE_RANGE and v is None:
            raise ValueError("start_date is required when filter_type is 'date_range'")
        return v

    @validator('end_date')
    def validate_end_date(cls, v, values):
        """Validate that end_date is provided and is >= start_date when filter_type is 'date_range'."""
        if values.get('filter_type') == FilterType.DATE_RANGE:
            if v is None:
                raise ValueError("end_date is required when filter_type is 'date_range'")
            start_date = values.get('start_date')
            if start_date and v < start_date:
                raise ValueError("end_date must be greater than or equal to start_date")
        return v

    class Config:
        """Pydantic model configuration."""
        use_enum_values = True
