#!/usr/bin/env python3
"""
Test script to verify the implemented changes work correctly.
"""

import sys
import asyncio
from datetime import datetime

# Add the app directory to the path
sys.path.append('.')

def test_conversation_tone_enum():
    """Test the new conversation tone enum values."""
    print("Testing ConversationToneEnum...")
    
    try:
        from app.db.models import ConversationToneEnum
        
        # Test all new enum values
        expected_values = [
            "professional", "warm", "enthusiastic", 
            "neutral", "dismissive", "impatient"
        ]
        
        actual_values = [tone.value for tone in ConversationToneEnum]
        
        print(f"Expected values: {expected_values}")
        print(f"Actual values: {actual_values}")
        
        for expected in expected_values:
            if expected not in actual_values:
                raise ValueError(f"Missing tone value: {expected}")
        
        print("✅ ConversationToneEnum test passed")
        return True
        
    except Exception as e:
        print(f"❌ ConversationToneEnum test failed: {str(e)}")
        return False


def test_date_formatting():
    """Test the date formatting utilities."""
    print("\nTesting date formatting utilities...")
    
    try:
        from app.utils.date_formatting import (
            format_date_general, format_date_range_general, 
            format_current_date_general
        )
        
        # Test current date
        current_date = format_current_date_general()
        print(f"Current date: {current_date}")
        
        # Test specific date
        test_date = format_date_general("2025-06-29")
        print(f"Test date (2025-06-29): {test_date}")
        
        # Test date range
        date_range = format_date_range_general("2025-06-29", "2025-06-30")
        print(f"Date range: {date_range}")
        
        # Test same date range
        same_date_range = format_date_range_general("2025-06-29", "2025-06-29")
        print(f"Same date range: {same_date_range}")
        
        # Verify format
        if "June" not in test_date or "2025" not in test_date:
            raise ValueError("Date format is incorrect")
        
        print("✅ Date formatting test passed")
        return True
        
    except Exception as e:
        print(f"❌ Date formatting test failed: {str(e)}")
        return False


async def test_health_check_service():
    """Test the health check service (basic import test)."""
    print("\nTesting health check service...")
    
    try:
        from app.services.health_check_service import health_check_service, APIHealthStatus, HealthCheckResult
        
        # Test data structures
        test_status = APIHealthStatus(
            service_name="Test",
            is_healthy=True,
            response_time_ms=100.0,
            error_message=None,
            status_code=200
        )
        
        test_result = HealthCheckResult(
            all_healthy=True,
            services=[test_status],
            failed_services=[],
            total_response_time_ms=100.0
        )
        
        # Test report formatting
        report = health_check_service.format_health_report(test_result)
        print(f"Health report sample: {report}")
        
        print("✅ Health check service test passed")
        return True
        
    except Exception as e:
        print(f"❌ Health check service test failed: {str(e)}")
        return False


def test_accuracy_service_import():
    """Test that accuracy service imports work."""
    print("\nTesting accuracy service imports...")
    
    try:
        from app.services.accuracy_service import analyze_information_accuracy
        print("✅ Accuracy service import test passed")
        return True
        
    except Exception as e:
        print(f"❌ Accuracy service import test failed: {str(e)}")
        return False


def test_email_service_imports():
    """Test that email service imports work."""
    print("\nTesting email service imports...")
    
    try:
        from app.services.ai_email_service import generate_and_send_agent_email
        from app.services.cron_job_service import _send_api_health_failure_email
        print("✅ Email service import test passed")
        return True
        
    except Exception as e:
        print(f"❌ Email service import test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Running implementation tests...\n")
    
    tests = [
        test_conversation_tone_enum(),
        test_date_formatting(),
        await test_health_check_service(),
        test_accuracy_service_import(),
        test_email_service_imports()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is ready.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
