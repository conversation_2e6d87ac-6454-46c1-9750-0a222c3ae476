from fastapi import APIRouter, HTTPException, Depends, Query, Body
from app.models.call_data import (
    CallDataRequest, CallDataDetail,
    ProcessCallsResponse
)
from app.models.analytics import CallAnalytics
from app.models.pagination import PaginatedResponse, PaginationMeta
from app.models.filters import (
    CallFilters, FilterType, SentimentFilter,
    ConversationFlagFilter
)
from app.services.db_service import (
    get_all_call_data, get_enhanced_call_data_by_id, count_call_data
)
from app.services.call_data_service import fetch_call_data, filter_valid_calls, process_calls_concurrently
from app.services.analytics_service import get_call_analytics
from app.services.auth_service import get_current_active_user
from app.services.email_reporting_service import EmailReportingService
from app.services.ai_email_service import generate_and_send_agent_email
from app.db.database import get_db
from app.db.models import User
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date
import logging
import math

router = APIRouter()
logger = logging.getLogger(__name__)


async def send_agent_emails_after_processing(
    processed_calls: List,
    date_range_start: str,
    date_range_end: str,
    db: Session
) -> List[dict]:
    """
    Send AI-powered emails to agents after their calls have been processed.

    Args:
        processed_calls: List of processed call data
        date_range_start: Start date for the processing period
        date_range_end: End date for the processing period
        db: Database session

    Returns:
        List of email results for each agent
    """
    if not processed_calls:
        logger.info("No processed calls to send emails for")
        return []

    try:
        # Extract unique agent names from processed calls
        agent_names = set()
        for call in processed_calls:
            if hasattr(call, 'agent_name') and call.agent_name:
                agent_names.add(call.agent_name)

        if not agent_names:
            logger.warning("No agent names found in processed calls")
            return []

        logger.info(f"Sending emails to {len(agent_names)} agents after call processing")

        # Initialize email reporting service for data collection
        email_service = EmailReportingService()

        # Process each agent individually
        email_results = []
        for agent_name in agent_names:
            try:
                # Generate agent report data
                report = email_service._generate_agent_report_data(
                    db=db,
                    agent_name=agent_name,
                    start_date=date_range_start.split(' ')[0],  # Extract date part from datetime string
                    end_date=date_range_end.split(' ')[0]      # Extract date part from datetime string
                )

                if not report:
                    logger.warning(f"No call data found for agent {agent_name}")
                    email_results.append({
                        "agent_name": agent_name,
                        "success": False,
                        "email_sent": False,
                        "error": "No call data found"
                    })
                    continue

                # Get agent email from database
                agent_email = email_service._get_agent_email(db, agent_name)
                if not agent_email:
                    logger.warning(f"No email address found for agent {agent_name}")
                    email_results.append({
                        "agent_name": agent_name,
                        "success": False,
                        "email_sent": False,
                        "error": "No email address found"
                    })
                    continue

                report.agent_email = agent_email

                # Use unified AI email service
                email_result = generate_and_send_agent_email(report)
                email_results.append(email_result)

                if email_result["success"]:
                    logger.info(f"Successfully sent email to agent {agent_name}")
                else:
                    logger.warning(f"Failed to send email to agent {agent_name}: {email_result.get('error', 'Unknown error')}")

            except Exception as e:
                logger.error(f"Error processing email for agent {agent_name}: {str(e)}")
                email_results.append({
                    "agent_name": agent_name,
                    "success": False,
                    "email_sent": False,
                    "error": str(e)
                })

        # Log summary
        successful_emails = sum(1 for result in email_results if result.get("email_sent", False))
        logger.info(f"Email processing complete: {successful_emails}/{len(agent_names)} emails sent successfully")

        return email_results

    except Exception as e:
        logger.error(f"Error in send_agent_emails_after_processing: {str(e)}")
        return []


@router.post("/process-calls", response_model=ProcessCallsResponse)
async def process_calls(
    request: CallDataRequest = Body(...),
    max_concurrent: int = Query(5, ge=1, le=20, description="Maximum number of concurrent tasks"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Process call data from MCube API for a given date range using concurrent processing.

    Fetches call data from the MCube API, filters calls based on duration (> 10 seconds),
    downloads audio files, transcribes them concurrently, and saves the results to the database.

    Note: Call data is only saved to the database if transcription is successful.
    Failed transcriptions are logged but not saved.

    Args:
        request: CallDataRequest with start_date and end_date
        max_concurrent: Maximum number of concurrent tasks (default: 5)
        db: Database session
        current_user: The authenticated user

    Returns:
        ProcessCallsResponse with message, number of calls processed, and list of processed call data

    Raises:
        HTTPException: If the API request fails or processing fails
    """
    try:
        # Fetch call data from the MCube API
        logger.info(f"Fetching call data from {request.start_date} to {request.end_date}")
        call_data_response = await fetch_call_data(request.start_date, request.end_date)

        # Check if the API request was successful
        if call_data_response.status != "succ":
            raise HTTPException(
                status_code=400,
                detail=f"Failed to fetch call data: {call_data_response.status}"
            )

        # If status is successful but message list is empty, return message with empty result
        if not call_data_response.msg or call_data_response.msg == []:
            logger.info(f"API returned success but no calls found for date range: {request.start_date} to {request.end_date}")
            return ProcessCallsResponse(
                message="No calls to process for the specified date range",
                calls_processed=0,
                data=[]
            )

        # Filter valid calls (duration > 10 seconds and has audio file)
        valid_calls = filter_valid_calls(call_data_response.msg)

        if not valid_calls:
            logger.warning("No valid calls found in the specified date range")
            return ProcessCallsResponse(
                message="No valid calls found for the specified date range (calls must be longer than 10 seconds and have an audio file)",
                calls_processed=0,
                data=[]
            )

        logger.info(f"Found {len(valid_calls)} valid calls to process with max concurrency of {max_concurrent}")

        # Process calls concurrently
        processed_calls = await process_calls_concurrently(
            calls=valid_calls,
            db=db,
            max_concurrent_tasks=max_concurrent
        )

        # Commit the database session
        db.commit()

        # Send AI-powered emails to agents after successful processing
        email_results = await send_agent_emails_after_processing(
            processed_calls=processed_calls,
            date_range_start=request.start_date,
            date_range_end=request.end_date,
            db=db
        )

        # Prepare response message with email information
        base_message = f"Successfully processed {len(processed_calls)} calls"
        if email_results:
            emails_sent = sum(1 for result in email_results if result.get("email_sent", False))
            base_message += f" | Emails sent to {emails_sent} agents"

        logger.info(f"Successfully processed {len(processed_calls)} calls and sent emails to agents")
        return ProcessCallsResponse(
            message=base_message,
            calls_processed=len(processed_calls),
            data=processed_calls
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Error processing calls: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process calls: {str(e)}")


@router.get("/calls", response_model=PaginatedResponse[CallDataDetail])
async def get_calls(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Number of records per page"),
    # Date filtering parameters
    filter_type: FilterType = Query(FilterType.ALL, description="Type of date filtering to apply"),
    specific_date: Optional[date] = Query(None, description="Specific date for filtering (YYYY-MM-DD)"),
    start_date: Optional[date] = Query(None, description="Start date for range filtering (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date for range filtering (YYYY-MM-DD)"),
    # Sentiment filtering parameters
    sentiment_filter: Optional[List[SentimentFilter]] = Query(None, description="Filter by overall sentiment values"),
    # Conversation filtering parameters
    conversation_flags: Optional[List[ConversationFlagFilter]] = Query(None, description="Filter by emotional flags"),
    # Agent name filtering parameter
    agent_name: Optional[str] = Query(None, description="Filter by agent name (case-insensitive partial matching)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all processed call data with their associated transcription details, with optional filtering.

    Returns a paginated list of call data with transcription information if available.
    Supports filtering by date ranges, sentiment values, conversation flags, and agent names.

    The sentiment_info in the response now includes the relevant_category field, which indicates
    the school information category that was identified as most relevant to each conversation.

    Args:
        page: Page number (starting from 1)
        per_page: Number of records per page
        filter_type: Type of date filtering (today, yesterday, specific_day, date_range, all)
        specific_date: Specific date for filtering (required when filter_type='specific_day')
        start_date: Start date for range filtering (required when filter_type='date_range')
        end_date: End date for range filtering (required when filter_type='date_range')
        sentiment_filter: Filter by overall sentiment values
        conversation_flags: Filter by emotional flags (frustration, confusion, urgency, satisfaction)
        agent_name: Filter by agent name (case-insensitive partial matching)
        db: Database session
        current_user: The authenticated user

    Returns:
        PaginatedResponse containing a list of CallDataDetail objects with transcription details
        and pagination metadata. Each call's sentiment_info includes relevant_category.

    Raises:
        HTTPException: If filter validation fails or there's an error retrieving data
    """
    try:
        # Validate filter parameters
        if filter_type == FilterType.SPECIFIC_DAY and specific_date is None:
            raise HTTPException(
                status_code=400,
                detail="specific_date is required when filter_type is 'specific_day'"
            )

        if filter_type == FilterType.DATE_RANGE:
            if start_date is None or end_date is None:
                raise HTTPException(
                    status_code=400,
                    detail="start_date and end_date are required when filter_type is 'date_range'"
                )
            if start_date > end_date:
                raise HTTPException(
                    status_code=400,
                    detail="start_date must be less than or equal to end_date"
                )

        # Create filters object
        filters = CallFilters(
            filter_type=filter_type,
            specific_date=specific_date,
            start_date=start_date,
            end_date=end_date,
            sentiment_filter=sentiment_filter,
            conversation_flags=conversation_flags,
            agent_name=agent_name
        )

        # Calculate skip value from page and per_page
        skip = (page - 1) * per_page

        # Get total count of records with filters applied
        total_count = count_call_data(db, filters)

        # Calculate pagination metadata
        page_count = math.ceil(total_count / per_page) if total_count > 0 else 0
        has_next = page < page_count
        has_previous = page > 1
        next_page = page + 1 if has_next else None
        previous_page = page - 1 if has_previous else None

        # Get calls for the current page with filters applied
        calls = get_all_call_data(db, skip, per_page, filters)

        # Create pagination metadata
        pagination_meta = PaginationMeta(
            total_count=total_count,
            page_count=page_count,
            current_page=page,
            per_page=per_page,
            has_next=has_next,
            has_previous=has_previous,
            next_page=next_page,
            previous_page=previous_page
        )

        # Return paginated response
        return PaginatedResponse(
            items=calls,
            meta=pagination_meta
        )
    except Exception as e:
        logger.error(f"Error retrieving call data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve call data: {str(e)}")


@router.get("/calls/{call_id}")
async def get_call_by_id(
    call_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get detailed information about a specific call by its ID.

    This endpoint returns complete call data with full transcription text,
    speaker segments, sentiment analysis, and information accuracy details.

    The sentiment_info in the response includes the relevant_category field, which indicates
    the school information category that was identified as most relevant to this conversation.

    Args:
        call_id: Database ID of the call record (not the call_id field)
        db: Database session
        current_user: The authenticated user

    Returns:
        Dict: Complete call data with all details including relevant_category in sentiment_info

    Raises:
        HTTPException: If the call is not found
    """
    try:
        # Get enhanced call data with full details using database ID
        call_data = get_enhanced_call_data_by_id(db, call_id)

        if not call_data:
            raise HTTPException(
                status_code=404,
                detail=f"Call with ID {call_id} not found"
            )

        # Return the complete call data directly
        return call_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving call data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve call data: {str(e)}")


@router.get("/analytics", response_model=CallAnalytics)
async def get_call_analytics_endpoint(
    # Date filtering parameters only
    filter_type: FilterType = Query(FilterType.ALL, description="Type of date filtering to apply"),
    specific_date: Optional[date] = Query(None, description="Specific date for filtering (YYYY-MM-DD)"),
    start_date: Optional[date] = Query(None, description="Start date for range filtering (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date for range filtering (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get analytics and statistics about processed calls, with optional date filtering.

    This endpoint returns comprehensive metrics about the calls in the database:
    - Total number of calls processed (for the specified date range)
    - Average call duration (for the specified date range)
    - Average information accuracy percentage (for the specified date range)
    - Complete sentiment distribution showing all sentiment types present
    - Complete emotional flag counts showing all flags present

    Supports date-based filtering only for simplified analytics.

    Args:
        filter_type: Type of date filtering (today, yesterday, specific_day, date_range, all)
        specific_date: Specific date for filtering (required when filter_type='specific_day')
        start_date: Start date for range filtering (required when filter_type='date_range')
        end_date: End date for range filtering (required when filter_type='date_range')
        db: Database session
        current_user: The authenticated user

    Returns:
        CallAnalytics: Object containing comprehensive analytics metrics for the specified time period

    Raises:
        HTTPException: If filter validation fails or there's an error retrieving the analytics
    """
    try:
        # Validate filter parameters
        if filter_type == FilterType.SPECIFIC_DAY and specific_date is None:
            raise HTTPException(
                status_code=400,
                detail="specific_date is required when filter_type is 'specific_day'"
            )

        if filter_type == FilterType.DATE_RANGE:
            if start_date is None or end_date is None:
                raise HTTPException(
                    status_code=400,
                    detail="start_date and end_date are required when filter_type is 'date_range'"
                )
            if start_date > end_date:
                raise HTTPException(
                    status_code=400,
                    detail="start_date must be less than or equal to end_date"
                )

        # Create filters object with date filtering only
        filters = CallFilters(
            filter_type=filter_type,
            specific_date=specific_date,
            start_date=start_date,
            end_date=end_date,
            sentiment_filter=None,  # No sentiment filtering for analytics
            conversation_flags=None  # No conversation flags filtering for analytics
        )

        # Get call analytics from the service with filters applied
        analytics = get_call_analytics(db, filters)
        return analytics
    except Exception as e:
        logger.error(f"Error retrieving call analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve call analytics: {str(e)}")