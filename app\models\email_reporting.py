from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime


class AgentSummaryStats(BaseModel):
    """Model for agent summary statistics."""

    agent_name: str = Field(..., description="Name of the agent")
    total_calls: int = Field(..., description="Total number of calls made by the agent")
    average_duration: float = Field(..., description="Average call duration in seconds")
    total_duration: int = Field(..., description="Total call duration in seconds")
    sentiment_breakdown: Dict[str, int] = Field(
        default_factory=dict,
        description="Breakdown of sentiment counts (positive, negative, neutral)"
    )
    most_common_sentiment: Optional[str] = Field(None, description="Most common sentiment for this agent")
    calls_with_transcription: int = Field(..., description="Number of calls that have transcription")
    calls_processed: int = Field(..., description="Number of calls that were successfully processed")


class AgentCallData(BaseModel):
    """Model for individual call data in agent report."""

    call_id: str = Field(..., description="Call ID")
    call_date: Optional[str] = Field(None, description="Date of the call")
    call_time: Optional[str] = Field(None, description="Time of the call")
    duration_seconds: int = Field(..., description="Call duration in seconds")
    duration_formatted: str = Field(..., description="Formatted duration (MM:SS)")
    student_name: Optional[str] = Field(None, description="Student name if available")
    student_id: Optional[str] = Field(None, description="Student ID if available")
    call_direction: Optional[str] = Field(None, description="Call direction (inbound/outbound)")
    call_source: Optional[str] = Field(None, description="Call source")
    has_transcription: bool = Field(..., description="Whether call has transcription")
    overall_sentiment: Optional[str] = Field(None, description="Overall sentiment of the call")
    key_points: List[str] = Field(default_factory=list, description="Key points from the call")
    summary: Optional[str] = Field(None, description="Call summary")
    relevant_category: Optional[str] = Field(None, description="Relevant information category")
    created_at: datetime = Field(..., description="When the call was created")

    # Information Accuracy Fields
    missed_information: List[str] = Field(default_factory=list, description="Information that was missed by the agent")
    incorrect_information: List[Dict[str, str]] = Field(default_factory=list, description="Information that was incorrectly provided")
    incomplete_information: List[Dict[str, str]] = Field(default_factory=list, description="Information that was incompletely provided")
    correct_information: List[str] = Field(default_factory=list, description="Information that was correctly provided")
    overall_assessment: Optional[str] = Field(None, description="Overall assessment of information accuracy")
    accuracy_percentage: Optional[Union[float, str]] = Field(None, description="Accuracy percentage (0-100) or 'NA' if not applicable")


class AgentEmailReport(BaseModel):
    """Model for complete agent email report."""

    agent_name: str = Field(..., description="Name of the agent")
    agent_email: Optional[str] = Field(None, description="Email address of the agent")
    report_period_start: str = Field(..., description="Start date of the reporting period")
    report_period_end: str = Field(..., description="End date of the reporting period")
    summary_stats: AgentSummaryStats = Field(..., description="Summary statistics for the agent")
    call_details: List[AgentCallData] = Field(..., description="Detailed call data for the agent")


class EmailReportingResult(BaseModel):
    """Model for email reporting operation result."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Result message")
    reports_generated: int = Field(0, description="Number of reports generated")
    emails_sent: int = Field(0, description="Number of emails sent successfully")
    errors: List[str] = Field(default_factory=list, description="List of errors encountered")
    agent_results: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Results for each agent"
    )


class EmailReportingConfig(BaseModel):
    """Model for AI email reporting configuration."""

    date_range_start: str = Field(..., description="Start date for the reporting period")
    date_range_end: str = Field(..., description="End date for the reporting period")
    include_summary_stats: bool = Field(True, description="Whether to include summary statistics")
    include_call_details: bool = Field(True, description="Whether to include detailed call data")
    use_ai_generation: bool = Field(True, description="Whether to use AI for email generation")
    fallback_on_ai_failure: bool = Field(True, description="Whether to use fallback templates if AI fails")
