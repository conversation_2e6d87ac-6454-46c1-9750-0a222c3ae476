from pydantic import BaseModel, <PERSON>, validator
from typing import List, Optional
from datetime import datetime
import re


def is_google_drive_url(url: str) -> bool:
    """
    Check if a string is a valid Google Drive URL.

    Args:
        url (str): The URL to check

    Returns:
        bool: True if it's a Google Drive URL, False otherwise
    """
    drive_patterns = [
        r'https://drive\.google\.com/file/d/[a-zA-Z0-9-_]+',
        r'https://drive\.google\.com/open\?id=[a-zA-Z0-9-_]+',
        r'https://docs\.google\.com/document/d/[a-zA-Z0-9-_]+',
        r'https://docs\.google\.com/presentation/d/[a-zA-Z0-9-_]+',
        r'https://docs\.google\.com/spreadsheets/d/[a-zA-Z0-9-_]+',
    ]

    return any(re.match(pattern, url.strip()) for pattern in drive_patterns)


class SchoolInfoRequest(BaseModel):
    """Model for creating or updating school information."""

    category: str = Field(
        ...,
        description="Category of school information (lowercase, no spaces)",
        min_length=1,
        max_length=50
    )
    content: str = Field(
        ...,
        description="Either detailed information content as text or a Google Drive URL to a document (PDF/DOCX)",
        min_length=1
    )

    @validator('category')
    def validate_category(cls, v):
        """Validate category format: lowercase, no spaces, alphanumeric with underscores."""
        if not v:
            raise ValueError("Category cannot be empty")

        # Check if category follows the required format
        if not re.match(r'^[a-z0-9_]+$', v):
            raise ValueError(
                "Category must be lowercase, contain only letters, numbers, and underscores, with no spaces"
            )

        return v

    @validator('content')
    def validate_content(cls, v):
        """Validate content is either non-empty text or a valid Google Drive URL."""
        if not v or not v.strip():
            raise ValueError("Content cannot be empty")

        content = v.strip()

        # If it looks like a URL, validate it's a Google Drive URL
        if content.startswith(('http://', 'https://')):
            if not is_google_drive_url(content):
                raise ValueError(
                    "URL must be a valid Google Drive sharing link. "
                    "Supported formats: Google Drive files, Google Docs, Sheets, or Slides"
                )

        return content


class SchoolInfoResponse(BaseModel):
    """Model for returning school information."""

    id: str = Field(..., description="Unique identifier")
    category: str = Field(..., description="Category of school information")
    content: str = Field(..., description="Detailed information content")
    created_at: datetime = Field(..., description="When the information was created")
    updated_at: datetime = Field(..., description="When the information was last updated")

    class Config:
        """Pydantic config."""
        from_attributes = True


class SchoolInfoUpdate(BaseModel):
    """Model for updating school information content."""

    content: str = Field(
        ...,
        description="Either updated information content as text or a Google Drive URL to a document (PDF/DOCX)",
        min_length=1
    )

    @validator('content')
    def validate_content(cls, v):
        """Validate content is either non-empty text or a valid Google Drive URL."""
        if not v or not v.strip():
            raise ValueError("Content cannot be empty")

        content = v.strip()

        # If it looks like a URL, validate it's a Google Drive URL
        if content.startswith(('http://', 'https://')):
            if not is_google_drive_url(content):
                raise ValueError(
                    "URL must be a valid Google Drive sharing link. "
                    "Supported formats: Google Drive files, Google Docs, Sheets, or Slides"
                )

        return content


class SchoolInfoList(BaseModel):
    """Model for listing all school information categories."""

    categories: List[SchoolInfoResponse] = Field(
        default_factory=list,
        description="List of all school information categories"
    )
    total_count: int = Field(
        0,
        description="Total number of categories"
    )


class SchoolInfoCategory(BaseModel):
    """Model for category summary information."""

    category: str = Field(..., description="Category name")
    content_preview: str = Field(..., description="Preview of content (first 100 characters)")
    created_at: datetime = Field(..., description="When the information was created")
    updated_at: datetime = Field(..., description="When the information was last updated")

    class Config:
        """Pydantic config."""
        from_attributes = True


# Predefined categories for initial setup
INITIAL_CATEGORIES = {
    "admission": {
        "content": """Admission Requirements and Procedures

**Eligibility Criteria:**
- BCA: Passed 10+2 examination
- B.Tech: Passed 10+2 examination with Physics, Mathematics as compulsory subjects
- M.Tech: B.Tech./MSc./MCA
- Ph.D: B.Tech./M.Tech./MSc./MBA

**Application Process:**
1. Submit online application form
2. Upload required documents
3. Pay application fee
4. Attend counseling session (if required)
5. Complete admission formalities

**Required Documents:**
- Academic transcripts
- Identity proof
- Address proof
- Passport size photographs
- Category certificate (if applicable)

**Important Dates:**
- Application deadline: Check university website
- Counseling dates: Will be announced
- Classes commence: As per academic calendar

For detailed information, contact the admission office."""
    },
    "fees": {
        "content": """Fee Structure and Payment Information

**Programme-wise Fee Structure:**
- BCA: Contact admission office for current fees
- B.Tech: Contact admission office for current fees
- M.Tech: Contact admission office for current fees
- Ph.D: Contact admission office for current fees

**Payment Methods:**
- Online payment through university portal
- Demand draft in favor of IILM University
- Bank transfer to university account

**Payment Schedule:**
- Semester-wise payment preferred
- Annual payment option available
- Installment facility may be available

**Additional Charges:**
- Registration fee
- Examination fee
- Library fee
- Laboratory fee
- Hostel fee (if applicable)

**Refund Policy:**
- Refund as per university guidelines
- Processing time: 30-45 working days
- Deductions as applicable

For current fee structure and payment details, contact the finance office."""
    },
    "enquiry": {
        "content": """General Enquiry Information and Contact Details

**Contact Information:**
- Phone: +91-124-2845000
- Email: <EMAIL>
- Website: www.iilm.edu

**Campus Address:**
IILM University
Plot No. 3, Knowledge Park-II
Greater Noida, Uttar Pradesh - 201306

**Office Hours:**
- Monday to Friday: 9:00 AM to 5:00 PM
- Saturday: 9:00 AM to 1:00 PM
- Sunday: Closed

**Department-wise Contacts:**
- Admissions: <EMAIL>
- Academic Affairs: <EMAIL>
- Finance: <EMAIL>
- Student Affairs: <EMAIL>

**Online Enquiry:**
- Visit university website
- Fill online enquiry form
- Live chat support available
- Social media channels active

**Campus Visit:**
- Prior appointment recommended
- Campus tours available
- Meet faculty and staff
- Infrastructure visit included

For specific queries, please contact the relevant department directly."""
    }
}
