from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class CallDataRequest(BaseModel):
    """Model for call data request parameters."""

    start_date: str = Field(
        ...,
        description="Start date and time in format 'YYYY-MM-DD HH:MM:SS'",
        example="2025-03-13 09:00:00"
    )
    end_date: str = Field(
        ...,
        description="End date and time in format 'YYYY-MM-DD HH:MM:SS'",
        example="2025-03-14 09:10:00"
    )


class CallDetails(BaseModel):
    """Model for individual call details."""

    call_history_id: str
    callid: str
    bid: str
    groupname: Optional[str] = None
    agentname: Optional[str] = None
    source: Optional[str] = None
    callto: Optional[str] = None
    starttime: Optional[str] = None
    endtime: Optional[str] = None
    pulse: Optional[str] = None
    filename: Optional[str] = None
    call_duration: Optional[int] = 0

    # Additional fields that might be useful
    call_type: Optional[str] = None
    direction: Optional[str] = None

    class Config:
        """Pydantic model configuration."""

        # Allow extra fields from the API response
        extra = "allow"


class CallDataResponse(BaseModel):
    """Model for call data API response."""

    status: str
    msg: List[Dict[str, Any]]


class ProcessedCallData(BaseModel):
    """Model for processed call data with transcription results."""

    call_id: str = Field(..., description="Unique call ID")
    call_duration: int = Field(0, description="Call duration in seconds")
    audio_url: Optional[str] = Field(None, description="URL of the audio file")
    transcription_id: Optional[str] = Field(None, description="ID of the transcription if processed")
    processed: bool = Field(False, description="Whether the call has been processed for transcription")
    created_at: datetime = Field(default_factory=datetime.now, description="When this record was created")

    # Additional fields from MCube API
    agent_name: Optional[str] = Field(None, description="Name of the agent who handled the call")
    call_date: Optional[str] = Field(None, description="Date of the call")
    call_time: Optional[str] = Field(None, description="Time of the call")
    group_name: Optional[str] = Field(None, description="Group name associated with the call")
    student_id: Optional[str] = Field(None, description="Student ID if available")
    student_name: Optional[str] = Field(None, description="Student name if available")
    call_direction: Optional[str] = Field(None, description="Direction of the call (inbound/outbound)")
    call_source: Optional[str] = Field(None, description="Source of the call")


class TranscriptionSummary(BaseModel):
    """Model for transcription summary in call data response."""

    id: str = Field(..., description="Transcription ID")
    text_preview: str = Field(..., description="Preview of transcribed text")
    duration_seconds: Optional[float] = Field(None, description="Duration of the audio in seconds")
    created_at: datetime = Field(..., description="When the transcription was created")
    speaker_count: int = Field(0, description="Number of speakers in the transcription")
    sentiment_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Summary of sentiment analysis including overall_sentiment, is_student_university_conversation, relevant_category, and summary"
    )
    accuracy_info: Optional[Dict[str, Any]] = Field(None, description="Summary of information accuracy analysis")


class CallDataDetail(BaseModel):
    """Model for detailed call data with transcription information."""

    id: str = Field(..., description="Database record ID")
    call_id: str = Field(..., description="Unique call ID")
    call_duration: int = Field(0, description="Call duration in seconds")
    audio_url: Optional[str] = Field(None, description="URL of the audio file")
    processed: bool = Field(False, description="Whether the call has been processed for transcription")
    created_at: datetime = Field(..., description="When this record was created")
    transcription_id: Optional[str] = Field(None, description="ID of the transcription if processed")
    transcription: Optional[TranscriptionSummary] = Field(None, description="Transcription summary if available")

    # Additional fields from MCube API
    agent_name: Optional[str] = Field(None, description="Name of the agent who handled the call")
    call_date: Optional[str] = Field(None, description="Date of the call")
    call_time: Optional[str] = Field(None, description="Time of the call")
    group_name: Optional[str] = Field(None, description="Group name associated with the call")
    student_id: Optional[str] = Field(None, description="Student ID if available")
    student_name: Optional[str] = Field(None, description="Student name if available")
    call_direction: Optional[str] = Field(None, description="Direction of the call (inbound/outbound)")
    call_source: Optional[str] = Field(None, description="Source of the call")


class TranscriptionFailure(BaseModel):
    """Model for tracking failed transcription attempts."""

    id: str = Field(..., description="Unique identifier for the failure record")
    call_id: str = Field(..., description="Call ID that failed transcription")
    start_date: Optional[str] = Field(None, description="Start date of the call in format 'YYYY-MM-DD HH:MM:SS'")
    end_date: Optional[str] = Field(None, description="End date of the call in format 'YYYY-MM-DD HH:MM:SS'")
    error_message: str = Field(..., description="Error message or reason for transcription failure")
    audio_url: Optional[str] = Field(None, description="URL of the audio file that failed transcription")
    created_at: datetime = Field(default_factory=datetime.now, description="When this failure was recorded")


class ProcessCallsResponse(BaseModel):
    """Model for process-calls endpoint response."""

    message: str = Field(..., description="Status message")
    calls_processed: int = Field(0, description="Number of calls processed")
    data: List[ProcessedCallData] = Field(default_factory=list, description="List of processed call data")


class CallDataWithFailure(BaseModel):
    """Model for combined call data and failure information."""

    call_data: Optional[Dict[str, Any]] = Field(None, description="Call data details if available")
    failure: Optional[TranscriptionFailure] = Field(None, description="Failure information if available")
    is_failed_call: bool = Field(False, description="Whether this is a failed call")

    class Config:
        """Pydantic model configuration."""
        arbitrary_types_allowed = True
