from pydantic import BaseModel, Field, EmailStr, validator
from typing import Dict, List
from datetime import datetime
import re


class BulkUpsertRequest(BaseModel):
    """Model for bulk upsert request with name-email pairs."""
    
    contacts: Dict[str, EmailStr] = Field(
        ...,
        description="Dictionary mapping contact names to email addresses",
        example={
            "john_doe": "<EMAIL>",
            "alice123": "<EMAIL>",
            "vishnu": "<EMAIL>",
            "samantha99": "<EMAIL>"
        }
    )

    @validator('contacts')
    def validate_contacts(cls, v):
        """Validate contacts dictionary."""
        if not v:
            raise ValueError("Contacts dictionary cannot be empty")
        
        if len(v) > 100:  # Reasonable limit for bulk operations
            raise ValueError("Cannot process more than 100 contacts at once")
        
        # Validate contact names
        for name in v.keys():
            if not name or not name.strip():
                raise ValueError("Contact name cannot be empty")
            
            # Check name length
            if len(name.strip()) > 50:
                raise ValueError(f"Contact name '{name}' is too long (max 50 characters)")
            
            # Check for valid characters (alphanumeric, underscore, hyphen, space)
            if not re.match(r'^[a-zA-Z0-9_\-\s]+$', name.strip()):
                raise ValueError(f"Contact name '{name}' contains invalid characters. Only letters, numbers, underscores, hyphens, and spaces are allowed")
        
        return v


class EmailContactRequest(BaseModel):
    """Model for creating or updating a single email contact."""
    
    email: EmailStr = Field(
        ...,
        description="Email address for the contact",
        example="<EMAIL>"
    )


class EmailContactResponse(BaseModel):
    """Model for email contact response."""
    
    id: str = Field(..., description="Contact ID")
    name: str = Field(..., description="Contact name")
    email: str = Field(..., description="Contact email address")
    created_at: datetime = Field(..., description="When the contact was created")
    updated_at: datetime = Field(..., description="When the contact was last updated")

    class Config:
        """Pydantic config."""
        from_attributes = True


class EmailContactUpdate(BaseModel):
    """Model for updating an email contact."""
    
    email: EmailStr = Field(
        ...,
        description="Updated email address for the contact",
        example="<EMAIL>"
    )


class BulkUpsertResponse(BaseModel):
    """Model for bulk upsert response."""
    
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    created_count: int = Field(..., description="Number of contacts created")
    updated_count: int = Field(..., description="Number of contacts updated")
    total_processed: int = Field(..., description="Total number of contacts processed")
    contacts: List[EmailContactResponse] = Field(
        default_factory=list,
        description="List of processed contacts"
    )


class EmailContactList(BaseModel):
    """Model for listing all email contacts."""
    
    contacts: List[EmailContactResponse] = Field(
        default_factory=list,
        description="List of all email contacts"
    )
    total_count: int = Field(
        0,
        description="Total number of contacts"
    )


class DeleteResponse(BaseModel):
    """Model for delete operation response."""
    
    success: bool = Field(..., description="Whether the deletion was successful")
    message: str = Field(..., description="Response message")
    deleted_contact: str = Field(..., description="Name of the deleted contact")
