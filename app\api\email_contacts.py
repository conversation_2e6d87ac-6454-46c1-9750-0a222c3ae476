from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.models import User
from app.models.email_contacts import (
    BulkUpsertRequest, EmailContactRequest, EmailContactResponse,
    EmailContactUpdate, BulkUpsertResponse, EmailContactList, DeleteResponse
)
from app.services.email_contacts_service import (
    bulk_upsert_email_contacts, get_all_email_contacts, get_email_contact_by_name,
    update_email_contact, delete_email_contact
)
from app.services.auth_service import get_current_active_user
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """
    Dependency to ensure the current user is an admin.

    Args:
        current_user: The authenticated user

    Returns:
        User: The admin user

    Raises:
        HTTPException: If user is not an admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


@router.post("/bulk-upsert", response_model=BulkUpsertResponse)
async def bulk_upsert_contacts(
    request: BulkUpsertRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Bulk create or update email contacts (admin only).
    
    This endpoint allows admin users to create or update multiple email contacts
    in a single operation. For each name-email pair in the input:
    - If the name already exists in the database, the email address is updated
    - If the name does not exist, a new contact record is created
    
    Args:
        request: BulkUpsertRequest containing name-email pairs
        db: Database session
        current_user: The authenticated admin user
        
    Returns:
        BulkUpsertResponse: Response with operation results
        
    Raises:
        HTTPException: If the operation fails or user is not admin
    """
    try:
        logger.info(f"Admin user {current_user.email} performing bulk upsert of {len(request.contacts)} contacts")
        
        response = bulk_upsert_email_contacts(db, request)
        
        logger.info(f"Bulk upsert completed: {response.created_count} created, {response.updated_count} updated")
        return response
        
    except ValueError as e:
        logger.error(f"Validation error during bulk upsert: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error during bulk upsert: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing the bulk upsert operation"
        )


@router.get("", response_model=EmailContactList)
async def get_contacts(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get all email contacts (admin only).
    
    This endpoint returns a list of all email contacts in the database,
    ordered by contact name.
    
    Args:
        db: Database session
        current_user: The authenticated admin user
        
    Returns:
        EmailContactList: List of all email contacts
        
    Raises:
        HTTPException: If the operation fails or user is not admin
    """
    try:
        logger.info(f"Admin user {current_user.email} retrieving all email contacts")
        
        contacts = get_all_email_contacts(db)
        
        logger.info(f"Retrieved {contacts.total_count} email contacts")
        return contacts
        
    except Exception as e:
        logger.error(f"Error retrieving email contacts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving email contacts"
        )


@router.put("/{name}", response_model=EmailContactResponse)
async def update_contact(
    name: str,
    update_data: EmailContactUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Update a specific email contact by name (admin only).
    
    This endpoint allows admin users to update the email address of an existing
    contact identified by name.
    
    Args:
        name: Contact name to update
        update_data: EmailContactUpdate with new email address
        db: Database session
        current_user: The authenticated admin user
        
    Returns:
        EmailContactResponse: Updated contact information
        
    Raises:
        HTTPException: If contact not found, operation fails, or user is not admin
    """
    try:
        logger.info(f"Admin user {current_user.email} updating contact '{name}'")
        
        updated_contact = update_email_contact(db, name, update_data)
        
        if not updated_contact:
            logger.warning(f"Contact '{name}' not found for update")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Contact with name '{name}' not found"
            )
        
        logger.info(f"Successfully updated contact '{name}'")
        return updated_contact
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating contact '{name}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the contact"
        )


@router.delete("/{name}", response_model=DeleteResponse)
async def delete_contact(
    name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Delete a specific email contact by name (admin only).
    
    This endpoint allows admin users to delete an existing contact
    identified by name.
    
    Args:
        name: Contact name to delete
        db: Database session
        current_user: The authenticated admin user
        
    Returns:
        DeleteResponse: Deletion confirmation
        
    Raises:
        HTTPException: If contact not found, operation fails, or user is not admin
    """
    try:
        logger.info(f"Admin user {current_user.email} deleting contact '{name}'")
        
        delete_response = delete_email_contact(db, name)
        
        if not delete_response:
            logger.warning(f"Contact '{name}' not found for deletion")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Contact with name '{name}' not found"
            )
        
        logger.info(f"Successfully deleted contact '{name}'")
        return delete_response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting contact '{name}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the contact"
        )
