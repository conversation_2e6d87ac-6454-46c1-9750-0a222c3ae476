#!/usr/bin/env python3
"""
Example script demonstrating Google Drive URL integration with School Information Management System.

This script shows how to:
1. Create school information using Google Drive URLs
2. Update existing information with new Google Drive documents
3. Handle errors when Google Drive documents are inaccessible
4. Maintain backward compatibility with plain text content

Prerequisites:
- The application server must be running
- You need admin credentials
- Google Drive documents must be publicly accessible or shared appropriately
"""

import requests
import json
from typing import Optional


class SchoolInfoClient:
    """Client for interacting with the School Information API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token: Optional[str] = None
    
    def login(self, email: str, password: str) -> bool:
        """Login and get authentication token."""
        response = requests.post(
            f"{self.base_url}/api/v1/auth/login",
            data={"username": email, "password": password}
        )
        
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            return True
        else:
            print(f"Login failed: {response.json()}")
            return False
    
    def _get_headers(self) -> dict:
        """Get headers with authentication token."""
        if not self.token:
            raise ValueError("Not authenticated. Please login first.")
        return {"Authorization": f"Bearer {self.token}"}
    
    def create_school_info(self, category: str, content: str) -> dict:
        """Create or update school information."""
        data = {"category": category, "content": content}
        response = requests.post(
            f"{self.base_url}/api/v1/school-info/categories",
            json=data,
            headers=self._get_headers()
        )
        
        if response.status_code in [200, 201]:
            return response.json()
        else:
            raise Exception(f"Failed to create school info: {response.json()}")
    
    def update_school_info(self, category: str, content: str) -> dict:
        """Update existing school information."""
        data = {"content": content}
        response = requests.put(
            f"{self.base_url}/api/v1/school-info/categories/{category}",
            json=data,
            headers=self._get_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to update school info: {response.json()}")
    
    def get_school_info(self, category: str) -> dict:
        """Get school information by category."""
        response = requests.get(
            f"{self.base_url}/api/v1/school-info/categories/{category}",
            headers=self._get_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to get school info: {response.json()}")


def main():
    """Demonstrate Google Drive URL integration."""
    
    # Initialize client
    client = SchoolInfoClient()
    
    # Login (replace with your admin credentials)
    print("Logging in...")
    if not client.login("<EMAIL>", "admin_password"):
        print("Failed to login. Please check your credentials.")
        return
    
    print("✓ Successfully logged in")
    
    # Example 1: Create school information using Google Drive URL
    print("\n1. Creating school information with Google Drive URL...")
    
    # Example Google Drive URLs (replace with your actual documents)
    admission_doc_url = "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
    
    try:
        result = client.create_school_info(
            category="admission_requirements",
            content=admission_doc_url
        )
        print(f"✓ Created admission requirements from Google Drive")
        print(f"  Category: {result['category']}")
        print(f"  Content length: {len(result['content'])} characters")
        print(f"  Content preview: {result['content'][:100]}...")
        
    except Exception as e:
        print(f"✗ Failed to create admission requirements: {e}")
    
    # Example 2: Create school information using plain text (backward compatibility)
    print("\n2. Creating school information with plain text...")
    
    try:
        result = client.create_school_info(
            category="contact_info",
            content="Phone: ******-0123\nEmail: <EMAIL>\nAddress: 123 University Ave"
        )
        print(f"✓ Created contact info with plain text")
        print(f"  Category: {result['category']}")
        print(f"  Content: {result['content']}")
        
    except Exception as e:
        print(f"✗ Failed to create contact info: {e}")
    
    # Example 3: Update existing information with Google Drive URL
    print("\n3. Updating school information with new Google Drive URL...")
    
    fees_doc_url = "https://drive.google.com/file/d/1234567890abcdefghijklmnopqrstuvwxyz"
    
    try:
        result = client.update_school_info(
            category="contact_info",  # Update the contact_info we just created
            content=fees_doc_url
        )
        print(f"✓ Updated contact_info with Google Drive document")
        print(f"  New content length: {len(result['content'])} characters")
        
    except Exception as e:
        print(f"✗ Failed to update with Google Drive URL: {e}")
        print("  This is expected if the Google Drive URL is not accessible")
    
    # Example 4: Handle invalid Google Drive URL
    print("\n4. Testing error handling with invalid URL...")
    
    try:
        result = client.create_school_info(
            category="invalid_url_test",
            content="https://example.com/not-a-google-drive-url.pdf"
        )
        print(f"✗ This should not succeed")
        
    except Exception as e:
        print(f"✓ Correctly rejected invalid URL: {e}")
    
    # Example 5: Retrieve and display school information
    print("\n5. Retrieving school information...")
    
    try:
        result = client.get_school_info("contact_info")
        print(f"✓ Retrieved contact_info")
        print(f"  Category: {result['category']}")
        print(f"  Created: {result['created_at']}")
        print(f"  Updated: {result['updated_at']}")
        print(f"  Content preview: {result['content'][:200]}...")
        
    except Exception as e:
        print(f"✗ Failed to retrieve school info: {e}")
    
    print("\n" + "="*50)
    print("Google Drive Integration Demo Complete!")
    print("="*50)
    
    print("\nKey Features Demonstrated:")
    print("• ✓ Google Drive URL validation")
    print("• ✓ Automatic text extraction from PDF/DOCX")
    print("• ✓ Backward compatibility with plain text")
    print("• ✓ Error handling for invalid URLs")
    print("• ✓ Error handling for inaccessible documents")
    
    print("\nSupported Google Drive URL formats:")
    print("• https://drive.google.com/file/d/FILE_ID")
    print("• https://drive.google.com/open?id=FILE_ID")
    print("• https://docs.google.com/document/d/FILE_ID")
    print("• https://docs.google.com/presentation/d/FILE_ID")
    print("• https://docs.google.com/spreadsheets/d/FILE_ID")


if __name__ == "__main__":
    main()
