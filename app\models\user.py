from pydantic import BaseModel, Field, EmailStr
from typing import Optional


class Token(BaseModel):
    """Model for JWT token response."""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Model for token data."""
    email: Optional[str] = None


class UserBase(BaseModel):
    """Base model for user data."""
    email: EmailStr = Field(..., description="User email address")


class UserLogin(UserBase):
    """Model for user login."""
    password: str = Field(..., description="User password")


class User(UserBase):
    """Model for user response."""
    id: str = Field(..., description="User ID")
    is_active: bool = Field(True, description="Whether the user is active")
    is_admin: bool = Field(False, description="Whether the user is an admin")

    class Config:
        """Pydantic config."""
        from_attributes = True
