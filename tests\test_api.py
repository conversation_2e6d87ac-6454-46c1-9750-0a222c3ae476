from fastapi.testclient import TestClient
from app.main import app
from datetime import date, timedelta

client = TestClient(app)


def test_read_root():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Welcome to the Transcription API"}


def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}


def test_get_calls_endpoint():
    """Test the basic calls endpoint without filters."""
    response = client.get("/api/v1/transcription/calls")
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data
    assert "total_count" in data["meta"]
    assert "page_count" in data["meta"]
    assert "current_page" in data["meta"]


def test_get_calls_with_date_filter():
    """Test the calls endpoint with date filtering."""
    today = date.today()
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "filter_type": "today",
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_date_range_filter():
    """Test the calls endpoint with date range filtering."""
    today = date.today()
    yesterday = today - timedelta(days=1)

    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "filter_type": "date_range",
            "start_date": yesterday.isoformat(),
            "end_date": today.isoformat(),
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_sentiment_filter():
    """Test the calls endpoint with overall sentiment filtering."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "sentiment_filter": ["positive", "very_positive"],
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_invalid_date_range():
    """Test the calls endpoint with invalid date range."""
    today = date.today()
    tomorrow = today + timedelta(days=1)

    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "filter_type": "date_range",
            "start_date": tomorrow.isoformat(),
            "end_date": today.isoformat(),  # end_date before start_date
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 400
    assert "start_date must be less than or equal to end_date" in response.json()["detail"]


def test_get_analytics_endpoint():
    """Test the basic analytics endpoint without filters."""
    response = client.get("/api/v1/transcription/analytics")
    assert response.status_code == 200
    data = response.json()
    assert "total_calls" in data
    assert "average_duration" in data
    assert "average_accuracy" in data
    assert "sentiment_distribution" in data
    assert "emotional_flags" in data


def test_get_analytics_with_filters():
    """Test the analytics endpoint with filtering."""
    response = client.get(
        "/api/v1/transcription/analytics",
        params={
            "filter_type": "today",
            "sentiment_filter": ["positive"]
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_calls" in data
    assert "average_duration" in data
    assert "average_accuracy" in data
    assert "sentiment_distribution" in data
    assert "emotional_flags" in data


def test_get_calls_with_conversation_flags_filter():
    """Test the calls endpoint with conversation flags filtering."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "conversation_flags": ["frustration", "confusion"],
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_analytics_with_conversation_flags():
    """Test the analytics endpoint with conversation flags filtering."""
    response = client.get(
        "/api/v1/transcription/analytics",
        params={
            "conversation_flags": ["frustration", "urgency"]
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_calls" in data
    assert "emotional_flags" in data


def test_get_calls_with_combined_filters():
    """Test the calls endpoint with combined sentiment and flags filtering."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "sentiment_filter": ["negative"],
            "conversation_flags": ["frustration"],
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_analytics_with_invalid_date_range():
    """Test the analytics endpoint with invalid date range."""
    today = date.today()
    tomorrow = today + timedelta(days=1)

    response = client.get(
        "/api/v1/transcription/analytics",
        params={
            "filter_type": "date_range",
            "start_date": tomorrow.isoformat(),
            "end_date": today.isoformat(),  # end_date before start_date
        }
    )
    assert response.status_code == 400
    assert "start_date must be less than or equal to end_date" in response.json()["detail"]


def test_get_calls_with_agent_name_filter():
    """Test the calls endpoint with agent name filtering."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "agent_name": "john",
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_agent_name_case_insensitive():
    """Test the calls endpoint with agent name filtering (case insensitive)."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "agent_name": "JOHN",  # Test uppercase
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_agent_name_partial_match():
    """Test the calls endpoint with agent name partial matching."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "agent_name": "joh",  # Partial match
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_combined_agent_and_sentiment_filters():
    """Test the calls endpoint with combined agent name and sentiment filtering."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "agent_name": "john",
            "sentiment_filter": ["positive"],
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_combined_agent_and_date_filters():
    """Test the calls endpoint with combined agent name and date filtering."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "agent_name": "john",
            "filter_type": "today",
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data


def test_get_calls_with_all_filters_including_agent():
    """Test the calls endpoint with all filters including agent name."""
    response = client.get(
        "/api/v1/transcription/calls",
        params={
            "agent_name": "john",
            "sentiment_filter": ["positive", "neutral"],
            "conversation_flags": ["satisfaction"],
            "filter_type": "today",
            "page": 1,
            "per_page": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "meta" in data
