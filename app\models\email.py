from pydantic import BaseModel, Field, EmailStr
from typing import Optional


class EmailRequest(BaseModel):
    """Model for simple email request."""
    
    to_email: EmailStr = Field(
        ...,
        description="Recipient email address",
        example="<EMAIL>"
    )
    subject: str = Field(
        ...,
        description="Email subject",
        min_length=1,
        max_length=200,
        example="Important Update"
    )
    message: str = Field(
        ...,
        description="Email message body",
        min_length=1,
        max_length=5000,
        example="This is the email message content."
    )


class EmailWithAttachmentRequest(BaseModel):
    """Model for email request with Excel attachment."""
    
    to_email: EmailStr = Field(
        ...,
        description="Recipient email address",
        example="<EMAIL>"
    )
    subject: str = Field(
        ...,
        description="Email subject",
        min_length=1,
        max_length=200,
        example="Report Attached"
    )
    message: str = Field(
        ...,
        description="Email message body",
        min_length=1,
        max_length=5000,
        example="Please find the attached Excel report."
    )
    excel_file_path: str = Field(
        ...,
        description="Path to the Excel file to attach",
        example="/path/to/report.xlsx"
    )


class EmailResponse(BaseModel):
    """Model for email response."""
    
    success: bool = Field(
        ...,
        description="Whether the email was sent successfully"
    )
    message: str = Field(
        ...,
        description="Response message",
        example="Email sent successfully"
    )
    recipient: Optional[str] = Field(
        None,
        description="Recipient email address",
        example="<EMAIL>"
    )
