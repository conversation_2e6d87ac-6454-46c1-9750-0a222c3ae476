#!/usr/bin/env python3
"""
Migration script to update conversation tone values from old enum to new enum.
This script will update existing PLEASANT and DULL values to the new enum values.
"""

import sys
import os
sys.path.append('.')

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.database import get_db

def migrate_conversation_tones():
    """Migrate old conversation tone values to new enum values."""
    
    print("🔄 Migrating conversation tone values...")
    print("=" * 60)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check current tone values in the database
        print("📊 Checking current conversation tone values...")
        
        result = db.execute(text("""
            SELECT tone, COUNT(*) as count 
            FROM conversation_tones 
            GROUP BY tone 
            ORDER BY count DESC
        """))
        
        current_tones = result.fetchall()
        print("Current tone distribution:")
        for tone, count in current_tones:
            print(f"  {tone}: {count} records")
        
        print("\n" + "=" * 60)
        
        # Update PLEASANT to WARM
        print("🔄 Updating PLEASANT → WARM...")
        pleasant_result = db.execute(text("""
            UPDATE conversation_tones 
            SET tone = 'WARM' 
            WHERE tone = 'PLEASANT'
        """))
        print(f"✅ Updated {pleasant_result.rowcount} PLEASANT records to WARM")
        
        # Update DULL to NEUTRAL
        print("🔄 Updating DULL → NEUTRAL...")
        dull_result = db.execute(text("""
            UPDATE conversation_tones 
            SET tone = 'NEUTRAL' 
            WHERE tone = 'DULL'
        """))
        print(f"✅ Updated {dull_result.rowcount} DULL records to NEUTRAL")
        
        # Commit the changes
        db.commit()
        print("\n✅ Migration completed successfully!")
        
        # Verify the changes
        print("\n" + "=" * 60)
        print("📊 Verifying updated tone values...")
        
        result = db.execute(text("""
            SELECT tone, COUNT(*) as count 
            FROM conversation_tones 
            GROUP BY tone 
            ORDER BY count DESC
        """))
        
        updated_tones = result.fetchall()
        print("Updated tone distribution:")
        for tone, count in updated_tones:
            print(f"  {tone}: {count} records")
        
        # Check for any remaining invalid tones
        valid_tones = ['PROFESSIONAL', 'WARM', 'ENTHUSIASTIC', 'NEUTRAL', 'DISMISSIVE', 'IMPATIENT']
        
        result = db.execute(text("""
            SELECT tone, COUNT(*) as count 
            FROM conversation_tones 
            WHERE tone NOT IN :valid_tones
            GROUP BY tone
        """), {"valid_tones": tuple(valid_tones)})
        
        invalid_tones = result.fetchall()
        if invalid_tones:
            print("\n⚠️  Warning: Found invalid tone values:")
            for tone, count in invalid_tones:
                print(f"  {tone}: {count} records")
        else:
            print("\n✅ All tone values are now valid!")
        
    except Exception as e:
        print(f"❌ Error during migration: {str(e)}")
        db.rollback()
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    migrate_conversation_tones()
