from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from typing import List

from app.db.database import get_db
from app.db.models import User
from app.models.school_info import (
    SchoolInfoRequest, SchoolInfoResponse, SchoolInfoUpdate,
    SchoolInfoList, SchoolInfoCategory
)
from app.services.school_info_service import (
    create_school_info, get_school_info_by_category, get_all_school_info,
    update_school_info, delete_school_info, get_school_info_categories_summary
)
from app.services.auth_service import get_current_active_user
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """
    Dependency to ensure the current user is an admin.

    Args:
        current_user: The authenticated user

    Returns:
        User: The admin user

    Raises:
        HTTPException: If user is not an admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


@router.post("/categories", response_model=SchoolInfoResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_school_info(
    school_info: SchoolInfoRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Create or update school information for a specific category.

    This endpoint implements upsert behavior - if the category already exists,
    it will update the content; otherwise, it will create a new record.

    **Content can be either:**
    - Plain text content
    - Google Drive URL to a document (PDF or DOCX)

    If a Google Drive URL is provided, the system will automatically download
    and extract text content from the document.

    **Admin access required.**

    Args:
        school_info: SchoolInfoRequest with category and content (text or Google Drive URL)
        db: Database session
        current_user: The authenticated admin user

    Returns:
        SchoolInfoResponse: Created or updated school information

    Raises:
        HTTPException: If validation fails, Google Drive processing fails, or database error occurs
    """
    try:
        result = create_school_info(db, school_info)
        logger.info(f"Admin {current_user.email} created/updated school info for category: {school_info.category}")
        return result
    except ValueError as e:
        # Handle Google Drive processing errors and validation errors
        logger.warning(f"Validation/processing error for school info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating/updating school info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create/update school information: {str(e)}"
        )


@router.get("/categories", response_model=SchoolInfoList)
async def get_all_school_information(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all school information categories.

    Returns a list of all available school information categories with their content.

    **Authentication required.**

    Args:
        db: Database session
        current_user: The authenticated user

    Returns:
        SchoolInfoList: List of all school information categories

    Raises:
        HTTPException: If database error occurs
    """
    try:
        result = get_all_school_info(db)
        logger.info(f"User {current_user.email} retrieved all school information")
        return result
    except Exception as e:
        logger.error(f"Error retrieving all school info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve school information: {str(e)}"
        )


@router.get("/categories/summary", response_model=List[SchoolInfoCategory])
async def get_school_info_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a summary of all school information categories with content preview.

    Returns a list of all categories with a preview of their content (first 100 characters).
    Useful for displaying category overviews without loading full content.

    **Authentication required.**

    Args:
        db: Database session
        current_user: The authenticated user

    Returns:
        List[SchoolInfoCategory]: List of category summaries

    Raises:
        HTTPException: If database error occurs
    """
    try:
        result = get_school_info_categories_summary(db)
        logger.info(f"User {current_user.email} retrieved school information summary")
        return result
    except Exception as e:
        logger.error(f"Error retrieving school info summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve school information summary: {str(e)}"
        )


@router.get("/categories/{category}", response_model=SchoolInfoResponse)
async def get_school_info_by_category_endpoint(
    category: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get school information for a specific category.

    **Authentication required.**

    Args:
        category: The category name (e.g., "admission", "fees", "enquiry")
        db: Database session
        current_user: The authenticated user

    Returns:
        SchoolInfoResponse: School information for the specified category

    Raises:
        HTTPException: If category not found or database error occurs
    """
    try:
        result = get_school_info_by_category(db, category)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"School information for category '{category}' not found"
            )

        logger.info(f"User {current_user.email} retrieved school info for category: {category}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving school info for category {category}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve school information: {str(e)}"
        )


@router.put("/categories/{category}", response_model=SchoolInfoResponse)
async def update_school_info_endpoint(
    category: str,
    update_data: SchoolInfoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Update school information for a specific category.

    **Content can be either:**
    - Plain text content
    - Google Drive URL to a document (PDF or DOCX)

    If a Google Drive URL is provided, the system will automatically download
    and extract text content from the document.

    **Admin access required.**

    Args:
        category: The category name to update
        update_data: SchoolInfoUpdate with new content (text or Google Drive URL)
        db: Database session
        current_user: The authenticated admin user

    Returns:
        SchoolInfoResponse: Updated school information

    Raises:
        HTTPException: If category not found, Google Drive processing fails, or database error occurs
    """
    try:
        result = update_school_info(db, category, update_data)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"School information for category '{category}' not found"
            )

        logger.info(f"Admin {current_user.email} updated school info for category: {category}")
        return result
    except HTTPException:
        raise
    except ValueError as e:
        # Handle Google Drive processing errors and validation errors
        logger.warning(f"Validation/processing error for school info update: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating school info for category {category}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update school information: {str(e)}"
        )


@router.delete("/categories/{category}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_school_info_endpoint(
    category: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Delete school information for a specific category.

    **Admin access required.**

    Args:
        category: The category name to delete
        db: Database session
        current_user: The authenticated admin user

    Raises:
        HTTPException: If category not found or database error occurs
    """
    try:
        success = delete_school_info(db, category)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"School information for category '{category}' not found"
            )

        logger.info(f"Admin {current_user.email} deleted school info for category: {category}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting school info for category {category}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete school information: {str(e)}"
        )
