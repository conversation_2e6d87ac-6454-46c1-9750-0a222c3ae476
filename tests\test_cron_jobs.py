import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime, timedelta
from app.main import app
from app.models.cron_job import CronJobType, CronJobStatus as CronJobStatusEnum
from app.services.cron_job_service import get_previous_day_date_range, get_custom_date_range

client = TestClient(app)


@pytest.fixture
def auth_headers():
    """Get authentication headers for testing."""
    # First login to get token
    login_response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "12345"}
    )
    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    return {}


@pytest.fixture
def mock_scheduler_service():
    """Mock the scheduler service."""
    with patch('app.api.cron_jobs.scheduler_service') as mock:
        yield mock


def test_get_previous_day_date_range():
    """Test the previous day date range calculation."""
    start_date, end_date = get_previous_day_date_range()
    
    # Verify format
    assert len(start_date) == 19  # YYYY-MM-DD HH:MM:SS
    assert len(end_date) == 19
    assert start_date.endswith(" 00:00:00")
    assert end_date.endswith(" 00:00:00")
    
    # Verify dates are consecutive days
    start_dt = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
    
    assert (end_dt - start_dt).days == 1


def test_get_custom_date_range():
    """Test custom date range calculation."""
    test_date = "2025-05-23"
    start_date, end_date = get_custom_date_range(test_date)
    
    assert start_date == "2025-05-23 00:00:00"
    assert end_date == "2025-05-24 00:00:00"


def test_get_custom_date_range_invalid_format():
    """Test custom date range with invalid format."""
    with pytest.raises(ValueError, match="Invalid date format"):
        get_custom_date_range("invalid-date")


def test_get_cron_job_status_success(mock_scheduler_service, auth_headers):
    """Test successful cron job status retrieval."""
    # Mock scheduler status
    mock_status = MagicMock()
    mock_status.enabled = True
    mock_status.scheduler_running = True
    mock_status.next_run_time = datetime.now()
    mock_status.last_execution = None
    mock_status.timezone = "UTC"
    mock_status.max_concurrent = 1
    
    mock_scheduler_service.get_scheduler_status.return_value = mock_status
    
    response = client.get(
        "/api/v1/cron-jobs/status",
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        data = response.json()
        assert data["enabled"] is True
        assert data["scheduler_running"] is True
        assert data["timezone"] == "UTC"
        assert data["max_concurrent"] == 1


def test_get_cron_job_status_without_auth():
    """Test that cron job status requires authentication."""
    response = client.get("/api/v1/cron-jobs/status")
    assert response.status_code == 401


@patch('app.api.cron_jobs.scheduler_service')
def test_trigger_daily_call_processing_success(mock_scheduler_service, auth_headers):
    """Test successful manual trigger of daily call processing."""
    # Mock successful execution
    mock_result = MagicMock()
    mock_result.success = True
    mock_result.message = "Successfully processed 5 calls"
    mock_result.execution = MagicMock()
    
    mock_scheduler_service.trigger_daily_call_processing_manually = AsyncMock(return_value=mock_result)
    
    response = client.post(
        "/api/v1/cron-jobs/trigger",
        json={},
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "processed" in data["message"]


@patch('app.api.cron_jobs.scheduler_service')
def test_trigger_daily_call_processing_with_overrides(mock_scheduler_service, auth_headers):
    """Test manual trigger with date and concurrency overrides."""
    mock_result = MagicMock()
    mock_result.success = True
    mock_result.message = "Successfully processed 3 calls"
    
    mock_scheduler_service.trigger_daily_call_processing_manually = AsyncMock(return_value=mock_result)
    
    response = client.post(
        "/api/v1/cron-jobs/trigger",
        json={
            "date_override": "2025-05-23",
            "max_concurrent": 2
        },
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        # Verify the service was called with correct parameters
        mock_scheduler_service.trigger_daily_call_processing_manually.assert_called_once_with(
            date_override="2025-05-23",
            max_concurrent_override=2
        )


def test_trigger_daily_call_processing_without_auth():
    """Test that manual trigger requires authentication."""
    response = client.post("/api/v1/cron-jobs/trigger", json={})
    assert response.status_code == 401


@patch('app.api.cron_jobs.scheduler_service')
def test_start_scheduler_success(mock_scheduler_service, auth_headers):
    """Test successful scheduler start."""
    mock_scheduler_service.is_running.return_value = False
    mock_scheduler_service.start_scheduler.return_value = None
    
    response = client.post(
        "/api/v1/cron-jobs/start",
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        data = response.json()
        assert "started successfully" in data["message"]


@patch('app.api.cron_jobs.scheduler_service')
def test_start_scheduler_already_running(mock_scheduler_service, auth_headers):
    """Test starting scheduler when already running."""
    mock_scheduler_service.is_running.return_value = True
    
    response = client.post(
        "/api/v1/cron-jobs/start",
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        data = response.json()
        assert "already running" in data["message"]


@patch('app.api.cron_jobs.scheduler_service')
def test_stop_scheduler_success(mock_scheduler_service, auth_headers):
    """Test successful scheduler stop."""
    mock_scheduler_service.is_running.return_value = True
    mock_scheduler_service.stop_scheduler.return_value = None
    
    response = client.post(
        "/api/v1/cron-jobs/stop",
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        data = response.json()
        assert "stopped successfully" in data["message"]


@patch('app.api.cron_jobs.scheduler_service')
def test_stop_scheduler_not_running(mock_scheduler_service, auth_headers):
    """Test stopping scheduler when not running."""
    mock_scheduler_service.is_running.return_value = False
    
    response = client.post(
        "/api/v1/cron-jobs/stop",
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        data = response.json()
        assert "not running" in data["message"]


def test_start_scheduler_without_auth():
    """Test that starting scheduler requires authentication."""
    response = client.post("/api/v1/cron-jobs/start")
    assert response.status_code == 401


def test_stop_scheduler_without_auth():
    """Test that stopping scheduler requires authentication."""
    response = client.post("/api/v1/cron-jobs/stop")
    assert response.status_code == 401


def test_invalid_date_override_format():
    """Test validation of date override format."""
    response = client.post(
        "/api/v1/cron-jobs/trigger",
        json={"date_override": "invalid-date"}
    )
    assert response.status_code == 422  # Validation error


def test_invalid_max_concurrent_value():
    """Test validation of max concurrent value."""
    response = client.post(
        "/api/v1/cron-jobs/trigger",
        json={"max_concurrent": 0}  # Below minimum
    )
    assert response.status_code == 422  # Validation error
    
    response = client.post(
        "/api/v1/cron-jobs/trigger",
        json={"max_concurrent": 25}  # Above maximum
    )
    assert response.status_code == 422  # Validation error
