from pydantic import BaseModel, Field
from typing import Dict, List
from datetime import datetime


class SentimentDistribution(BaseModel):
    """Model for sentiment distribution statistics."""

    positive_count: int = Field(0, description="Number of calls with positive sentiment")
    negative_count: int = Field(0, description="Number of calls with negative sentiment")
    neutral_count: int = Field(0, description="Number of calls with neutral sentiment")
    positive_percentage: float = Field(0.0, description="Percentage of calls with positive sentiment")
    negative_percentage: float = Field(0.0, description="Percentage of calls with negative sentiment")
    neutral_percentage: float = Field(0.0, description="Percentage of calls with neutral sentiment")


class EmotionalFlagCounts(BaseModel):
    """Model for emotional flag counts."""

    frustration: int = Field(0, description="Number of calls flagged with frustration")
    confusion: int = Field(0, description="Number of calls flagged with confusion")
    urgency: int = Field(0, description="Number of calls flagged with urgency")
    satisfaction: int = Field(0, description="Number of calls flagged with satisfaction")
    abusive: int = Field(0, description="Number of calls flagged with abusive behavior")


class ConversationToneCounts(BaseModel):
    """Model for conversation tone counts."""

    pleasant: int = Field(0, description="Number of calls with pleasant tone")
    dull: int = Field(0, description="Number of calls with dull tone")
    unanalyzed: int = Field(0, description="Number of calls without tone analysis")


class CallAnalytics(BaseModel):
    """Model for call analytics response."""

    total_calls: int = Field(0, description="Total number of calls processed and saved in the database")
    average_duration: float = Field(0.0, description="Average duration of all calls (in seconds)")
    average_accuracy: float = Field(0.0, description="Average information accuracy percentage across all calls")
    positive_sentiment_percentage: float = Field(0.0, description="Percentage of calls with positive sentiment")
    sentiment_distribution: SentimentDistribution = Field(
        default_factory=SentimentDistribution,
        description="Breakdown of sentiment distribution across calls"
    )
    emotional_flags: EmotionalFlagCounts = Field(
        default_factory=EmotionalFlagCounts,
        description="Counts of emotional flags across calls"
    )
    conversation_tones: ConversationToneCounts = Field(
        default_factory=ConversationToneCounts,
        description="Distribution of conversation tones across calls"
    )
    generated_at: datetime = Field(default_factory=datetime.now, description="When these analytics were generated")
