# Google Drive Integration for School Information Management

## Overview

The School Information Management System now supports Google Drive URLs as content input, allowing administrators to store institutional information by simply providing a link to a Google Drive document. The system automatically downloads and extracts text content from PDF and DOCX documents.

## Features

### ✅ Supported Document Types
- **PDF files** (.pdf)
- **Microsoft Word documents** (.docx)

### ✅ Supported Google Drive URL Formats
- `https://drive.google.com/file/d/FILE_ID`
- `https://drive.google.com/open?id=FILE_ID`
- `https://docs.google.com/document/d/FILE_ID`
- `https://docs.google.com/presentation/d/FILE_ID`
- `https://docs.google.com/spreadsheets/d/FILE_ID`

### ✅ Key Benefits
- **Automatic text extraction** from documents
- **Backward compatibility** with plain text input
- **Comprehensive error handling** for invalid URLs and inaccessible documents
- **Admin-only access** for creating/updating content
- **Seamless integration** with existing AI analysis features

## API Usage

### Creating School Information with Google Drive URL

```bash
POST /api/v1/school-info/categories
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "category": "admission_requirements",
  "content": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
}
```

### Creating School Information with Plain Text (Backward Compatibility)

```bash
POST /api/v1/school-info/categories
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "category": "contact_info",
  "content": "Phone: ******-0123\nEmail: <EMAIL>\nAddress: 123 University Ave"
}
```

### Updating School Information

```bash
PUT /api/v1/school-info/categories/{category}
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "content": "https://drive.google.com/file/d/NEW_FILE_ID"
}
```

## Error Handling

### Invalid URL Format
```json
{
  "detail": "URL must be a valid Google Drive sharing link. Supported formats: Google Drive files, Google Docs, Sheets, or Slides"
}
```

### Document Access Failure
```json
{
  "detail": "Failed to extract text from Google Drive document. Please check the URL and ensure the document is accessible."
}
```

### Empty Document
```json
{
  "detail": "The Google Drive document appears to be empty or contains no extractable text."
}
```

### Network/Processing Error
```json
{
  "detail": "Error processing Google Drive document: [specific error message]"
}
```

## Document Sharing Requirements

For the system to successfully extract text from Google Drive documents, ensure:

1. **Document is shared** with appropriate permissions
2. **Link sharing is enabled** (at minimum "Anyone with the link can view")
3. **Document contains extractable text** (not just images)
4. **Document is in supported format** (PDF or DOCX)

### Setting Up Document Sharing

1. Open your document in Google Drive
2. Click the "Share" button
3. Under "General access", select "Anyone with the link"
4. Set permission to "Viewer" (minimum required)
5. Copy the sharing link

## Implementation Details

### Content Processing Flow

1. **URL Validation**: Check if content is a valid Google Drive URL
2. **Document Download**: Download the document from Google Drive
3. **Format Detection**: Identify document type (PDF/DOCX) by file header
4. **Text Extraction**: Extract text content using appropriate library
5. **Content Storage**: Store extracted text in database
6. **Cleanup**: Remove temporary files

### Dependencies

The Google Drive integration uses the following components:

- `app/utils/drive_download.py` - Core download and extraction functionality
- `PyPDF2` - PDF text extraction
- `python-docx` - DOCX text extraction
- `requests` - HTTP client for downloading files

### Database Storage

- **Original URL is not stored** - only the extracted text content
- **Same database schema** - no changes to existing table structure
- **Transparent to AI analysis** - extracted text works with existing features

## Testing

### Unit Tests
```bash
# Run Google Drive integration tests
pytest tests/test_school_info_drive_integration.py -v

# Run all school info tests
pytest tests/test_school_info.py -v
```

### Manual Testing
```bash
# Run the example script
python examples/google_drive_school_info_example.py
```

## Security Considerations

### Access Control
- **Admin-only operations** - Only admin users can create/update school information
- **No credential storage** - System doesn't store Google Drive credentials
- **Public document access** - Documents must be publicly accessible via sharing link

### Data Privacy
- **Temporary file cleanup** - Downloaded files are automatically deleted
- **No file retention** - Only extracted text is stored, not original documents
- **Standard authentication** - Uses existing JWT-based authentication

## Troubleshooting

### Common Issues

**Issue**: "Failed to extract text from Google Drive document"
- **Solution**: Check document sharing permissions and ensure link is publicly accessible

**Issue**: "URL must be a valid Google Drive sharing link"
- **Solution**: Verify URL format matches supported patterns

**Issue**: "Document appears to be empty"
- **Solution**: Ensure document contains extractable text (not just images or scanned content)

**Issue**: Network timeouts
- **Solution**: Check internet connectivity and Google Drive service status

### Debugging

Enable debug logging to see detailed processing information:

```python
import logging
logging.getLogger('app.services.school_info_service').setLevel(logging.DEBUG)
logging.getLogger('app.utils.drive_download').setLevel(logging.DEBUG)
```

## Migration Guide

### For Existing Installations

No database migration is required. The new functionality is fully backward compatible:

1. **Existing data** continues to work unchanged
2. **Plain text input** still supported
3. **API endpoints** maintain same interface
4. **No breaking changes** to existing functionality

### For New Installations

1. Install required dependencies:
   ```bash
   pip install PyPDF2 python-docx
   ```

2. Ensure network access to Google Drive:
   ```bash
   # Test connectivity
   curl -I https://drive.google.com
   ```

3. Configure logging (optional):
   ```python
   # In your application configuration
   LOGGING_CONFIG = {
       'loggers': {
           'app.services.school_info_service': {'level': 'INFO'},
           'app.utils.drive_download': {'level': 'INFO'}
       }
   }
   ```

## Future Enhancements

Potential improvements for future versions:

- **Additional document formats** (Google Sheets, PowerPoint, etc.)
- **Batch document processing** for multiple URLs
- **Document version tracking** and update notifications
- **Enhanced error recovery** with retry mechanisms
- **Document metadata extraction** (title, author, etc.)
- **Integration with Google Drive API** for authenticated access
