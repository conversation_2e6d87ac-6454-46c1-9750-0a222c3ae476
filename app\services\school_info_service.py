from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.db.models import SchoolInformation
from app.models.school_info import (
    SchoolInfoRequest, SchoolInfoResponse, SchoolInfoUpdate,
    SchoolInfoList, SchoolInfoCategory, INITIAL_CATEGORIES, is_google_drive_url
)
from app.utils.drive_download import download_and_extract_text_from_drive
from typing import List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def process_content_input(content: str) -> tuple[str, Optional[str]]:
    """
    Process content input - either extract text from Google Drive URL or return as-is.

    Args:
        content: Either plain text content or Google Drive URL

    Returns:
        tuple: (processed_content, error_message)
               processed_content is the final text content to store
               error_message is None if successful, error string if failed
    """
    content = content.strip()

    # Check if content is a Google Drive URL
    if content.startswith(('http://', 'https://')) and is_google_drive_url(content):
        logger.info(f"Processing Google Drive URL: {content}")

        try:
            extracted_text = download_and_extract_text_from_drive(content, keep_file=False)

            if extracted_text is None:
                return "", "Failed to extract text from Google Drive document. Please check the URL and ensure the document is accessible."

            if not extracted_text.strip():
                return "", "The Google Drive document appears to be empty or contains no extractable text."

            logger.info(f"Successfully extracted {len(extracted_text)} characters from Google Drive document")
            return extracted_text.strip(), None

        except Exception as e:
            logger.error(f"Error processing Google Drive URL {content}: {str(e)}")
            return "", f"Error processing Google Drive document: {str(e)}"

    # Return content as-is if it's not a Google Drive URL
    return content, None


def create_school_info(db: Session, school_info: SchoolInfoRequest) -> SchoolInfoResponse:
    """
    Create or update school information for a specific category (upsert behavior).

    Args:
        db: Database session
        school_info: SchoolInfoRequest object with category and content (text or Google Drive URL)

    Returns:
        SchoolInfoResponse: Created or updated school information

    Raises:
        Exception: If database operation fails or Google Drive processing fails
    """
    try:
        # Process content input (extract from Google Drive if URL, otherwise use as-is)
        processed_content, error_message = process_content_input(school_info.content)

        if error_message:
            raise ValueError(error_message)

        # Check if category already exists
        existing_info = db.query(SchoolInformation).filter(
            SchoolInformation.category == school_info.category
        ).first()

        if existing_info:
            # Update existing record
            existing_info.content = processed_content
            existing_info.updated_at = datetime.now()
            db.commit()
            db.refresh(existing_info)
            logger.info(f"Updated school information for category: {school_info.category}")
            return SchoolInfoResponse.from_orm(existing_info)
        else:
            # Create new record
            db_school_info = SchoolInformation(
                category=school_info.category,
                content=processed_content
            )
            db.add(db_school_info)
            db.commit()
            db.refresh(db_school_info)
            logger.info(f"Created school information for category: {school_info.category}")
            return SchoolInfoResponse.from_orm(db_school_info)

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating/updating school information: {str(e)}")
        raise


def get_school_info_by_category(db: Session, category: str) -> Optional[SchoolInfoResponse]:
    """
    Get school information for a specific category.

    Args:
        db: Database session
        category: Category name

    Returns:
        Optional[SchoolInfoResponse]: School information if found, None otherwise
    """
    try:
        school_info = db.query(SchoolInformation).filter(
            SchoolInformation.category == category
        ).first()

        if school_info:
            return SchoolInfoResponse.from_orm(school_info)
        return None

    except Exception as e:
        logger.error(f"Error retrieving school information for category {category}: {str(e)}")
        raise


def get_all_school_info(db: Session) -> SchoolInfoList:
    """
    Get all school information categories.

    Args:
        db: Database session

    Returns:
        SchoolInfoList: List of all school information
    """
    try:
        school_infos = db.query(SchoolInformation).order_by(SchoolInformation.category).all()

        categories = [SchoolInfoResponse.from_orm(info) for info in school_infos]

        return SchoolInfoList(
            categories=categories,
            total_count=len(categories)
        )

    except Exception as e:
        logger.error(f"Error retrieving all school information: {str(e)}")
        raise


def update_school_info(db: Session, category: str, update_data: SchoolInfoUpdate) -> Optional[SchoolInfoResponse]:
    """
    Update school information for a specific category.

    Args:
        db: Database session
        category: Category name
        update_data: SchoolInfoUpdate object with new content (text or Google Drive URL)

    Returns:
        Optional[SchoolInfoResponse]: Updated school information if found, None otherwise

    Raises:
        Exception: If Google Drive processing fails
    """
    try:
        school_info = db.query(SchoolInformation).filter(
            SchoolInformation.category == category
        ).first()

        if not school_info:
            return None

        # Process content input (extract from Google Drive if URL, otherwise use as-is)
        processed_content, error_message = process_content_input(update_data.content)

        if error_message:
            raise ValueError(error_message)

        school_info.content = processed_content
        school_info.updated_at = datetime.now()

        db.commit()
        db.refresh(school_info)

        logger.info(f"Updated school information for category: {category}")
        return SchoolInfoResponse.from_orm(school_info)

    except Exception as e:
        db.rollback()
        logger.error(f"Error updating school information for category {category}: {str(e)}")
        raise


def delete_school_info(db: Session, category: str) -> bool:
    """
    Delete school information for a specific category.

    Args:
        db: Database session
        category: Category name

    Returns:
        bool: True if deleted, False if not found
    """
    try:
        school_info = db.query(SchoolInformation).filter(
            SchoolInformation.category == category
        ).first()

        if not school_info:
            return False

        db.delete(school_info)
        db.commit()

        logger.info(f"Deleted school information for category: {category}")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting school information for category {category}: {str(e)}")
        raise


def get_school_info_categories_summary(db: Session) -> List[SchoolInfoCategory]:
    """
    Get a summary of all school information categories with content preview.

    Args:
        db: Database session

    Returns:
        List[SchoolInfoCategory]: List of category summaries
    """
    try:
        school_infos = db.query(SchoolInformation).order_by(SchoolInformation.category).all()

        categories = []
        for info in school_infos:
            # Create content preview (first 100 characters)
            content_preview = info.content[:100] + "..." if len(info.content) > 100 else info.content

            category = SchoolInfoCategory(
                category=info.category,
                content_preview=content_preview,
                created_at=info.created_at,
                updated_at=info.updated_at
            )
            categories.append(category)

        return categories

    except Exception as e:
        logger.error(f"Error retrieving school information categories summary: {str(e)}")
        raise


def initialize_default_categories(db: Session) -> None:
    """
    Initialize the database with default school information categories.

    Args:
        db: Database session
    """
    try:
        for category, data in INITIAL_CATEGORIES.items():
            # Check if category already exists
            existing = db.query(SchoolInformation).filter(
                SchoolInformation.category == category
            ).first()

            if not existing:
                school_info = SchoolInformation(
                    category=category,
                    content=data["content"]
                )
                db.add(school_info)
                logger.info(f"Initialized default category: {category}")

        db.commit()
        logger.info("Default school information categories initialized successfully")

    except Exception as e:
        db.rollback()
        logger.error(f"Error initializing default categories: {str(e)}")
        raise


def get_all_school_info_for_ai(db: Session) -> dict:
    """
    Get all school information formatted for AI analysis.

    Args:
        db: Database session

    Returns:
        dict: Dictionary with category as key and content as value
    """
    try:
        school_infos = db.query(SchoolInformation).all()

        ai_data = {}
        for info in school_infos:
            ai_data[info.category] = info.content

        return ai_data

    except Exception as e:
        logger.error(f"Error retrieving school information for AI: {str(e)}")
        raise
