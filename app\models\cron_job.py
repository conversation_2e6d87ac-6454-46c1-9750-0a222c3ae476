from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class CronJobStatus(str, Enum):
    """Enum for cron job execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class CronJobType(str, Enum):
    """Enum for cron job types."""
    DAILY_CALL_PROCESSING = "daily_call_processing"


class CronJobExecution(BaseModel):
    """Model for cron job execution tracking."""

    id: Optional[str] = Field(None, description="Execution ID")
    job_type: CronJobType = Field(..., description="Type of cron job")
    status: CronJobStatus = Field(CronJobStatus.PENDING, description="Execution status")
    start_time: datetime = Field(default_factory=datetime.now, description="When the job started")
    end_time: Optional[datetime] = Field(None, description="When the job completed")
    duration_seconds: Optional[float] = Field(None, description="Job execution duration in seconds")

    # Job-specific data
    date_range_start: Optional[str] = Field(None, description="Start date for call processing")
    date_range_end: Optional[str] = Field(None, description="End date for call processing")
    calls_processed: Optional[int] = Field(None, description="Number of calls processed")
    calls_found: Optional[int] = Field(None, description="Number of calls found")
    max_concurrent: Optional[int] = Field(None, description="Max concurrent tasks used")

    # Error information
    error_message: Optional[str] = Field(None, description="Error message if job failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")

    # Metadata
    triggered_by: str = Field("cron", description="How the job was triggered (cron, manual, etc.)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    class Config:
        """Pydantic config."""
        from_attributes = True


class CronJobExecutionResponse(BaseModel):
    """Model for cron job execution response."""

    success: bool = Field(..., description="Whether the job execution was successful")
    message: str = Field(..., description="Response message")
    execution: Optional[CronJobExecution] = Field(None, description="Job execution details")


class CronJobSystemStatus(BaseModel):
    """Model for cron job system status."""

    enabled: bool = Field(..., description="Whether cron jobs are enabled")
    scheduler_running: bool = Field(..., description="Whether the scheduler is running")
    next_run_time: Optional[datetime] = Field(None, description="Next scheduled run time")
    last_execution: Optional[CronJobExecution] = Field(None, description="Last job execution")
    timezone: str = Field(..., description="Scheduler timezone")
    max_concurrent: int = Field(..., description="Max concurrent tasks for cron jobs")


class ManualTriggerRequest(BaseModel):
    """Model for manually triggering a cron job."""

    date_override: Optional[str] = Field(
        None,
        description="Override date for processing (YYYY-MM-DD format). If not provided, uses previous day.",
        example="2025-05-23"
    )
    max_concurrent: Optional[int] = Field(
        None,
        description="Override max concurrent tasks. If not provided, uses configured default.",
        ge=1,
        le=20
    )
