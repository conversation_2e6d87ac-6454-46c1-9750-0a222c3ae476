"""initial schema

Revision ID: 59da5cfca8fb
Revises: 
Create Date: 2025-05-26 13:24:30.483632

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '59da5cfca8fb'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cron_job_executions',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('job_type', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('date_range_start', sa.String(), nullable=True),
    sa.Column('date_range_end', sa.String(), nullable=True),
    sa.Column('calls_processed', sa.Integer(), nullable=True),
    sa.Column('calls_found', sa.Integer(), nullable=True),
    sa.Column('max_concurrent', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('error_details', sa.JSON(), nullable=True),
    sa.Column('triggered_by', sa.String(), nullable=False),
    sa.Column('job_metadata', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cron_job_executions_job_type'), 'cron_job_executions', ['job_type'], unique=False)
    op.create_index(op.f('ix_cron_job_executions_start_time'), 'cron_job_executions', ['start_time'], unique=False)
    op.create_index(op.f('ix_cron_job_executions_status'), 'cron_job_executions', ['status'], unique=False)
    op.create_table('email_contacts',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_contacts_name'), 'email_contacts', ['name'], unique=True)
    op.create_table('school_information',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_school_information_category'), 'school_information', ['category'], unique=True)
    op.create_table('transcription_failures',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('call_id', sa.String(), nullable=False),
    sa.Column('start_date', sa.String(), nullable=True),
    sa.Column('end_date', sa.String(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=False),
    sa.Column('audio_url', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transcription_failures_call_id'), 'transcription_failures', ['call_id'], unique=False)
    op.create_table('transcriptions',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('filename', sa.String(), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('transcription_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transcriptions_filename'), 'transcriptions', ['filename'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('hashed_password', sa.String(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_table('call_data',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('call_id', sa.String(), nullable=False),
    sa.Column('audio_url', sa.String(), nullable=True),
    sa.Column('call_duration', sa.Integer(), nullable=True),
    sa.Column('transcription_id', sa.String(), nullable=True),
    sa.Column('processed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('agent_name', sa.String(), nullable=True),
    sa.Column('call_date', sa.String(), nullable=True),
    sa.Column('call_time', sa.String(), nullable=True),
    sa.Column('group_name', sa.String(), nullable=True),
    sa.Column('student_id', sa.String(), nullable=True),
    sa.Column('student_name', sa.String(), nullable=True),
    sa.Column('call_direction', sa.String(), nullable=True),
    sa.Column('call_source', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['transcription_id'], ['transcriptions.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_call_data_call_id'), 'call_data', ['call_id'], unique=True)
    op.create_table('conversation_flags',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('transcription_id', sa.String(), nullable=False),
    sa.Column('flag_type', sa.Enum('FRUSTRATION', 'CONFUSION', 'URGENCY', 'SATISFACTION', 'ABUSIVE', name='conversationflagenum'), nullable=False),
    sa.Column('is_present', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['transcription_id'], ['transcriptions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversation_flags_flag_type'), 'conversation_flags', ['flag_type'], unique=False)
    op.create_index(op.f('ix_conversation_flags_is_present'), 'conversation_flags', ['is_present'], unique=False)
    op.create_index(op.f('ix_conversation_flags_transcription_id'), 'conversation_flags', ['transcription_id'], unique=False)
    op.create_table('conversation_tones',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('transcription_id', sa.String(), nullable=False),
    sa.Column('tone', sa.Enum('PLEASANT', 'DULL', name='conversationtoneenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['transcription_id'], ['transcriptions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('transcription_id')
    )
    op.create_index(op.f('ix_conversation_tones_tone'), 'conversation_tones', ['tone'], unique=False)
    op.create_table('information_accuracy',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('transcription_id', sa.String(), nullable=False),
    sa.Column('missed_information', sa.JSON(), nullable=True),
    sa.Column('incorrect_information', sa.JSON(), nullable=True),
    sa.Column('incomplete_information', sa.JSON(), nullable=True),
    sa.Column('correct_information', sa.JSON(), nullable=True),
    sa.Column('overall_assessment', sa.Text(), nullable=True),
    sa.Column('accuracy_percentage', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['transcription_id'], ['transcriptions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('transcription_id')
    )
    op.create_table('sentiment_analysis',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('transcription_id', sa.String(), nullable=False),
    sa.Column('is_student_university_conversation', sa.Boolean(), nullable=True),
    sa.Column('overall_sentiment', sa.Enum('VERY_NEGATIVE', 'NEGATIVE', 'NEUTRAL', 'POSITIVE', 'VERY_POSITIVE', name='sentimentenum'), nullable=True),
    sa.Column('relevant_category', sa.String(), nullable=True),
    sa.Column('key_points', sa.JSON(), nullable=True),
    sa.Column('speaker_roles', sa.JSON(), nullable=True),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('action_items', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['transcription_id'], ['transcriptions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('transcription_id')
    )
    op.create_index(op.f('ix_sentiment_analysis_overall_sentiment'), 'sentiment_analysis', ['overall_sentiment'], unique=False)
    op.create_index(op.f('ix_sentiment_analysis_relevant_category'), 'sentiment_analysis', ['relevant_category'], unique=False)
    op.create_table('speaker_segments',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('transcription_id', sa.String(), nullable=False),
    sa.Column('speaker', sa.String(), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.ForeignKeyConstraint(['transcription_id'], ['transcriptions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('speaker_segments')
    op.drop_index(op.f('ix_sentiment_analysis_relevant_category'), table_name='sentiment_analysis')
    op.drop_index(op.f('ix_sentiment_analysis_overall_sentiment'), table_name='sentiment_analysis')
    op.drop_table('sentiment_analysis')
    op.drop_table('information_accuracy')
    op.drop_index(op.f('ix_conversation_tones_tone'), table_name='conversation_tones')
    op.drop_table('conversation_tones')
    op.drop_index(op.f('ix_conversation_flags_transcription_id'), table_name='conversation_flags')
    op.drop_index(op.f('ix_conversation_flags_is_present'), table_name='conversation_flags')
    op.drop_index(op.f('ix_conversation_flags_flag_type'), table_name='conversation_flags')
    op.drop_table('conversation_flags')
    op.drop_index(op.f('ix_call_data_call_id'), table_name='call_data')
    op.drop_table('call_data')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_transcriptions_filename'), table_name='transcriptions')
    op.drop_table('transcriptions')
    op.drop_index(op.f('ix_transcription_failures_call_id'), table_name='transcription_failures')
    op.drop_table('transcription_failures')
    op.drop_index(op.f('ix_school_information_category'), table_name='school_information')
    op.drop_table('school_information')
    op.drop_index(op.f('ix_email_contacts_name'), table_name='email_contacts')
    op.drop_table('email_contacts')
    op.drop_index(op.f('ix_cron_job_executions_status'), table_name='cron_job_executions')
    op.drop_index(op.f('ix_cron_job_executions_start_time'), table_name='cron_job_executions')
    op.drop_index(op.f('ix_cron_job_executions_job_type'), table_name='cron_job_executions')
    op.drop_table('cron_job_executions')
    # ### end Alembic commands ###
