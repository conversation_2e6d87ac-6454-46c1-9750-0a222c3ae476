from pydantic import BaseModel, Field
from typing import List, Generic, TypeVar, Optional

T = TypeVar('T')


class PaginationMeta(BaseModel):
    """Model for pagination metadata."""
    
    total_count: int = Field(..., description="Total number of records")
    page_count: int = Field(..., description="Total number of pages")
    current_page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of items per page")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")
    next_page: Optional[int] = Field(None, description="Next page number if available")
    previous_page: Optional[int] = Field(None, description="Previous page number if available")


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic model for paginated responses."""
    
    items: List[T] = Field(..., description="List of items")
    meta: PaginationMeta = Field(..., description="Pagination metadata")
