import asyncio
import logging
from datetime import datetime
from typing import Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.jobstores.memory import MemoryJobStore

from app.core.settings import settings
from app.services.cron_job_service import execute_daily_call_processing
from app.models.cron_job import CronJobSystemStatus, CronJobType
from app.db.database import SessionLocal

logger = logging.getLogger(__name__)


class SchedulerService:
    """Service for managing scheduled jobs."""

    def __init__(self):
        self.scheduler: Optional[AsyncIOScheduler] = None
        self._is_running = False

    def initialize_scheduler(self) -> None:
        """Initialize the APScheduler instance."""
        if self.scheduler is not None:
            logger.warning("Scheduler already initialized")
            return

        try:
            # Configure job stores and executors
            jobstores = {
                'default': MemoryJobStore()
            }

            executors = {
                'default': AsyncIOExecutor()
            }

            job_defaults = {
                'coalesce': True,  # Combine multiple pending executions into one
                'max_instances': 1,  # Only one instance of each job can run at a time
                'misfire_grace_time': 300  # 5 minutes grace time for missed jobs
            }

            # Create scheduler
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone=settings.CRON_TIMEZONE
            )

            logger.info(f"Scheduler initialized with timezone: {settings.CRON_TIMEZONE}")

        except Exception as e:
            logger.error(f"Error initializing scheduler: {str(e)}")
            raise

    def start_scheduler(self) -> None:
        """Start the scheduler and add jobs."""
        if not settings.CRON_ENABLED:
            logger.info("Cron jobs are disabled in configuration")
            return

        if self.scheduler is None:
            self.initialize_scheduler()

        if self._is_running:
            logger.warning("Scheduler is already running")
            return

        try:
            # Start the scheduler
            self.scheduler.start()
            self._is_running = True

            # Add the daily call processing job
            self.add_daily_call_processing_job()

            logger.info("Scheduler started successfully")

        except Exception as e:
            logger.error(f"Error starting scheduler: {str(e)}")
            self._is_running = False
            raise

    def stop_scheduler(self) -> None:
        """Stop the scheduler."""
        if self.scheduler is None or not self._is_running:
            logger.info("Scheduler is not running")
            return

        try:
            self.scheduler.shutdown(wait=False)
            self._is_running = False
            logger.info("Scheduler stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping scheduler: {str(e)}")

    def add_daily_call_processing_job(self) -> None:
        """Add the daily call processing job to the scheduler."""
        if self.scheduler is None:
            logger.error("Scheduler not initialized")
            return

        try:
            # Create cron trigger for daily execution at 12:00 PM IST
            trigger = CronTrigger(
                hour=7,
                minute=30,
                second=0,
                timezone=settings.CRON_TIMEZONE
            )

            # Add the job
            self.scheduler.add_job(
                func=self._execute_daily_call_processing_wrapper,
                trigger=trigger,
                id='daily_call_processing',
                name='Daily Call Processing',
                replace_existing=True
            )

            logger.info(f"Daily call processing job added to scheduler (runs at 12:00 PM daily in {settings.CRON_TIMEZONE})")

        except Exception as e:
            logger.error(f"Error adding daily call processing job: {str(e)}")
            raise

    async def _execute_daily_call_processing_wrapper(self) -> None:
        """Wrapper function for executing daily call processing job."""
        try:
            logger.info("Starting scheduled daily call processing job")

            result = await execute_daily_call_processing(triggered_by="cron")

            if result.success:
                logger.info(f"Scheduled daily call processing completed successfully: {result.message}")
            else:
                logger.error(f"Scheduled daily call processing failed: {result.message}")

        except Exception as e:
            logger.error(f"Error in scheduled daily call processing: {str(e)}")

    def get_scheduler_status(self) -> CronJobSystemStatus:
        """Get the current status of the scheduler."""
        try:
            # Get last execution from database
            db = SessionLocal()
            try:
                from app.services.cron_job_service import get_last_cron_job_execution
                last_execution = get_last_cron_job_execution(db, CronJobType.DAILY_CALL_PROCESSING)
            finally:
                db.close()

            # Get next run time
            next_run_time = None
            if self.scheduler and self._is_running:
                job = self.scheduler.get_job('daily_call_processing')
                if job:
                    next_run_time = job.next_run_time

            return CronJobSystemStatus(
                enabled=settings.CRON_ENABLED,
                scheduler_running=self._is_running,
                next_run_time=next_run_time,
                last_execution=last_execution,
                timezone=settings.CRON_TIMEZONE,
                max_concurrent=settings.CRON_MAX_CONCURRENT
            )

        except Exception as e:
            logger.error(f"Error getting scheduler status: {str(e)}")
            return CronJobSystemStatus(
                enabled=settings.CRON_ENABLED,
                scheduler_running=False,
                next_run_time=None,
                last_execution=None,
                timezone=settings.CRON_TIMEZONE,
                max_concurrent=settings.CRON_MAX_CONCURRENT
            )

    def is_running(self) -> bool:
        """Check if the scheduler is running."""
        return self._is_running

    async def trigger_daily_call_processing_manually(
        self,
        date_override: Optional[str] = None,
        max_concurrent_override: Optional[int] = None
    ) -> None:
        """Manually trigger the daily call processing job."""
        try:
            logger.info(f"Manually triggering daily call processing job (date_override: {date_override})")

            result = await execute_daily_call_processing(
                date_override=date_override,
                max_concurrent_override=max_concurrent_override,
                triggered_by="manual"
            )

            if result.success:
                logger.info(f"Manual daily call processing completed successfully: {result.message}")
            else:
                logger.error(f"Manual daily call processing failed: {result.message}")

            return result

        except Exception as e:
            logger.error(f"Error in manual daily call processing trigger: {str(e)}")
            raise


# Global scheduler instance
scheduler_service = SchedulerService()
