"""
Health Check Service

This module provides health check functionality for external APIs used by the application.
It tests connectivity to Deepgram and OpenAI APIs to ensure they are operational before
running cron jobs or other critical operations.
"""

import logging
import asyncio
from typing import Dict, List, Optional
from dataclasses import dataclass
from openai import OpenAI
import httpx
from app.core.settings import settings

logger = logging.getLogger(__name__)


@dataclass
class APIHealthStatus:
    """Data class for API health status."""
    service_name: str
    is_healthy: bool
    response_time_ms: Optional[float]
    error_message: Optional[str]
    status_code: Optional[int]


@dataclass
class HealthCheckResult:
    """Data class for overall health check result."""
    all_healthy: bool
    services: List[APIHealthStatus]
    failed_services: List[str]
    total_response_time_ms: float


class HealthCheckService:
    """Service for checking the health of external APIs."""

    def __init__(self):
        """Initialize the health check service."""
        self.timeout_seconds = 10  # Timeout for API calls

    async def check_all_apis(self) -> HealthCheckResult:
        """
        Check the health of all external APIs.
        
        Returns:
            HealthCheckResult: Overall health status of all APIs
        """
        logger.info("Starting health check for all APIs")
        
        # Run health checks concurrently
        tasks = [
            self._check_openai_health(),
            self._check_deepgram_health()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        services = []
        failed_services = []
        total_response_time = 0.0
        
        for result in results:
            if isinstance(result, Exception):
                # Handle unexpected exceptions
                logger.error(f"Unexpected error during health check: {str(result)}")
                services.append(APIHealthStatus(
                    service_name="Unknown",
                    is_healthy=False,
                    response_time_ms=None,
                    error_message=str(result),
                    status_code=None
                ))
                failed_services.append("Unknown")
            else:
                services.append(result)
                if result.response_time_ms:
                    total_response_time += result.response_time_ms
                if not result.is_healthy:
                    failed_services.append(result.service_name)
        
        all_healthy = len(failed_services) == 0
        
        health_result = HealthCheckResult(
            all_healthy=all_healthy,
            services=services,
            failed_services=failed_services,
            total_response_time_ms=total_response_time
        )
        
        if all_healthy:
            logger.info(f"All APIs are healthy (total response time: {total_response_time:.2f}ms)")
        else:
            logger.warning(f"Some APIs are unhealthy: {', '.join(failed_services)}")
        
        return health_result

    async def _check_openai_health(self) -> APIHealthStatus:
        """
        Check OpenAI API health.
        
        Returns:
            APIHealthStatus: Health status of OpenAI API
        """
        import time
        start_time = time.time()
        
        try:
            logger.debug("Checking OpenAI API health")
            
            # Initialize OpenAI client
            client = OpenAI(api_key=settings.OPENAI_API_KEY)
            
            # Make a simple API call to test connectivity
            response = client.chat.completions.create(
                model="gpt-4o-mini",  # Use a lightweight model for health check
                messages=[{"role": "user", "content": "Health check"}],
                max_tokens=5,
                timeout=self.timeout_seconds
            )
            
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            if response and response.choices:
                logger.debug(f"OpenAI API health check successful ({response_time:.2f}ms)")
                return APIHealthStatus(
                    service_name="OpenAI",
                    is_healthy=True,
                    response_time_ms=response_time,
                    error_message=None,
                    status_code=200
                )
            else:
                logger.warning("OpenAI API returned empty response")
                return APIHealthStatus(
                    service_name="OpenAI",
                    is_healthy=False,
                    response_time_ms=response_time,
                    error_message="Empty response from OpenAI API",
                    status_code=200
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            error_msg = str(e)
            logger.error(f"OpenAI API health check failed: {error_msg}")
            
            return APIHealthStatus(
                service_name="OpenAI",
                is_healthy=False,
                response_time_ms=response_time,
                error_message=error_msg,
                status_code=None
            )

    async def _check_deepgram_health(self) -> APIHealthStatus:
        """
        Check Deepgram API health.
        
        Returns:
            APIHealthStatus: Health status of Deepgram API
        """
        import time
        start_time = time.time()
        
        try:
            logger.debug("Checking Deepgram API health")
            
            # Use httpx for async HTTP requests
            async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
                # Make a simple request to Deepgram's projects endpoint
                headers = {
                    "Authorization": f"Token {settings.DEEPGRAM_API_KEY}",
                    "Content-Type": "application/json"
                }
                
                response = await client.get(
                    "https://api.deepgram.com/v1/projects",
                    headers=headers
                )
                
                response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                
                if response.status_code == 200:
                    logger.debug(f"Deepgram API health check successful ({response_time:.2f}ms)")
                    return APIHealthStatus(
                        service_name="Deepgram",
                        is_healthy=True,
                        response_time_ms=response_time,
                        error_message=None,
                        status_code=response.status_code
                    )
                else:
                    logger.warning(f"Deepgram API returned status code: {response.status_code}")
                    return APIHealthStatus(
                        service_name="Deepgram",
                        is_healthy=False,
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status_code}: {response.text}",
                        status_code=response.status_code
                    )
                    
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            error_msg = str(e)
            logger.error(f"Deepgram API health check failed: {error_msg}")
            
            return APIHealthStatus(
                service_name="Deepgram",
                is_healthy=False,
                response_time_ms=response_time,
                error_message=error_msg,
                status_code=None
            )

    def format_health_report(self, health_result: HealthCheckResult) -> str:
        """
        Format health check result into a readable report.
        
        Args:
            health_result: Health check result to format
            
        Returns:
            str: Formatted health report
        """
        if health_result.all_healthy:
            return f"✅ All APIs are healthy (Total response time: {health_result.total_response_time_ms:.2f}ms)"
        
        report_lines = ["❌ API Health Check Failed:"]
        
        for service in health_result.services:
            status_icon = "✅" if service.is_healthy else "❌"
            response_time = f"{service.response_time_ms:.2f}ms" if service.response_time_ms else "N/A"
            
            if service.is_healthy:
                report_lines.append(f"  {status_icon} {service.service_name}: Healthy ({response_time})")
            else:
                error_info = service.error_message or "Unknown error"
                if service.status_code:
                    error_info = f"HTTP {service.status_code} - {error_info}"
                report_lines.append(f"  {status_icon} {service.service_name}: Failed - {error_info}")
        
        return "\n".join(report_lines)


# Global instance
health_check_service = HealthCheckService()
