import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.db.database import get_db
from app.db.models import Base, User, SchoolInformation
from app.core.security import get_password_hash
from app.models.school_info import is_google_drive_url
from app.services.school_info_service import process_content_input
import uuid

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_school_info_drive.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create tables
Base.metadata.create_all(bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture
def test_db():
    """Create a test database session."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def admin_user(test_db):
    """Create an admin user for testing."""
    user = User(
        id=str(uuid.uuid4()),
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        is_active=True,
        is_admin=True
    )
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    return user


@pytest.fixture
def admin_token(admin_user):
    """Get admin authentication token."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": admin_user.email, "password": "testpassword"}
    )
    return response.json()["access_token"]


class TestGoogleDriveURLValidation:
    """Test cases for Google Drive URL validation."""

    def test_valid_google_drive_urls(self):
        """Test that valid Google Drive URLs are recognized."""
        valid_urls = [
            "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
            "https://drive.google.com/open?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
            "https://docs.google.com/document/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
            "https://docs.google.com/presentation/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
            "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        ]
        
        for url in valid_urls:
            assert is_google_drive_url(url), f"URL should be valid: {url}"

    def test_invalid_google_drive_urls(self):
        """Test that invalid URLs are not recognized as Google Drive URLs."""
        invalid_urls = [
            "https://example.com/file.pdf",
            "https://dropbox.com/s/abc123/file.pdf",
            "https://onedrive.live.com/file.pdf",
            "not a url at all",
            "ftp://drive.google.com/file/d/123",
            "https://drive.google.com/invalid/path",
        ]
        
        for url in invalid_urls:
            assert not is_google_drive_url(url), f"URL should be invalid: {url}"


class TestContentProcessing:
    """Test cases for content processing functionality."""

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_process_google_drive_url_success(self, mock_download):
        """Test successful processing of Google Drive URL."""
        mock_download.return_value = "Extracted text content from document"
        
        url = "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        content, error = process_content_input(url)
        
        assert error is None
        assert content == "Extracted text content from document"
        mock_download.assert_called_once_with(url, keep_file=False)

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_process_google_drive_url_failure(self, mock_download):
        """Test handling of Google Drive URL processing failure."""
        mock_download.return_value = None
        
        url = "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        content, error = process_content_input(url)
        
        assert content == ""
        assert "Failed to extract text from Google Drive document" in error

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_process_google_drive_url_empty_content(self, mock_download):
        """Test handling of empty content from Google Drive document."""
        mock_download.return_value = "   "  # Only whitespace
        
        url = "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        content, error = process_content_input(url)
        
        assert content == ""
        assert "appears to be empty" in error

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_process_google_drive_url_exception(self, mock_download):
        """Test handling of exceptions during Google Drive processing."""
        mock_download.side_effect = Exception("Network error")
        
        url = "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        content, error = process_content_input(url)
        
        assert content == ""
        assert "Error processing Google Drive document: Network error" in error

    def test_process_plain_text_content(self):
        """Test processing of plain text content (non-URL)."""
        text_content = "This is plain text content for the school information."
        content, error = process_content_input(text_content)
        
        assert error is None
        assert content == text_content


class TestSchoolInfoAPIWithGoogleDrive:
    """Test cases for School Info API with Google Drive integration."""

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_create_school_info_with_google_drive_url(self, mock_download, admin_token):
        """Test creating school information with Google Drive URL."""
        mock_download.return_value = "Extracted admission requirements from Google Drive document"
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        data = {
            "category": "admission_drive",
            "content": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        }

        response = client.post("/api/v1/school-info/categories", json=data, headers=headers)

        assert response.status_code == 201
        result = response.json()
        assert result["category"] == "admission_drive"
        assert result["content"] == "Extracted admission requirements from Google Drive document"
        mock_download.assert_called_once()

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_create_school_info_with_invalid_google_drive_url(self, mock_download, admin_token):
        """Test creating school information with invalid Google Drive URL."""
        mock_download.return_value = None
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        data = {
            "category": "admission_drive_fail",
            "content": "https://drive.google.com/file/d/invalid_file_id"
        }

        response = client.post("/api/v1/school-info/categories", json=data, headers=headers)

        assert response.status_code == 400
        assert "Failed to extract text from Google Drive document" in response.json()["detail"]

    def test_create_school_info_with_invalid_url_format(self, admin_token):
        """Test creating school information with invalid URL format."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        data = {
            "category": "admission_invalid_url",
            "content": "https://example.com/not-a-google-drive-url.pdf"
        }

        response = client.post("/api/v1/school-info/categories", json=data, headers=headers)

        assert response.status_code == 422  # Validation error
        assert "URL must be a valid Google Drive sharing link" in str(response.json())

    @patch('app.services.school_info_service.download_and_extract_text_from_drive')
    def test_update_school_info_with_google_drive_url(self, mock_download, admin_token, test_db):
        """Test updating school information with Google Drive URL."""
        # First create a school info record
        school_info = SchoolInformation(
            category="test_update_drive",
            content="Original content"
        )
        test_db.add(school_info)
        test_db.commit()
        
        mock_download.return_value = "Updated content from Google Drive"
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        data = {
            "content": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        }

        response = client.put(f"/api/v1/school-info/categories/{school_info.category}", json=data, headers=headers)

        assert response.status_code == 200
        result = response.json()
        assert result["content"] == "Updated content from Google Drive"
        mock_download.assert_called_once()

    def test_backward_compatibility_with_plain_text(self, admin_token):
        """Test that plain text content still works (backward compatibility)."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        data = {
            "category": "plain_text_test",
            "content": "This is plain text content that should work as before."
        }

        response = client.post("/api/v1/school-info/categories", json=data, headers=headers)

        assert response.status_code == 201
        result = response.json()
        assert result["category"] == "plain_text_test"
        assert result["content"] == "This is plain text content that should work as before."
