from deepgram import DeepgramClient
from deepgram import PrerecordedOptions, FileSource
from sqlalchemy.orm import Session
from app.core.settings import settings
from app.services.sentiment_service import analyze_conversation_sentiment
from app.models.transcription import SpeakerSegment
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


def transcribe_audio_file(file_content: bytes,
                               db: Session = None,
                               smart_format: bool = True, punctuate: bool = True,
                               paragraphs: bool = True, diarize: bool = True,
                               profanity_filter: bool = False, analyze_sentiment: bool = True):
    """
    Transcribe audio using Deepgram API with all enhanced features enabled by default.

    Args:
        file_content: The binary content of the audio file
        db: Database session for sentiment analysis (required if analyze_sentiment=True)
        smart_format: Whether to apply smart formatting (default: True)
        punctuate: Whether to add punctuation (default: True)
        paragraphs: Whether to split into paragraphs (default: True)
        diarize: Whether to recognize speaker changes (default: True)
        profanity_filter: Whether to filter profanity (default: True)
        analyze_sentiment: Whether to analyze sentiment of the conversation (default: True)

    Returns:
        dict: Transcription result with text and metadata
    """
    try:
        # Initialize the Deepgram client
        deepgram = DeepgramClient(api_key=settings.DEEPGRAM_API_KEY)

        # Create the payload with the file content
        payload: FileSource = {
            "buffer": file_content,
        }

        # Configure transcription options
        options = PrerecordedOptions(
            model="nova-3",
            language="multi",
            smart_format=smart_format,
            punctuate=punctuate,
            paragraphs=paragraphs,
            diarize=diarize,
            profanity_filter=profanity_filter

        )

        # Send the audio for transcription using the REST API
        response = deepgram.listen.rest.v("1").transcribe_file(
            payload, options, timeout=300
        )

        # Check if the response has the expected structure
        if (not hasattr(response, 'results') or
            not hasattr(response.results, 'channels') or
            len(response.results.channels) == 0 or
            not hasattr(response.results.channels[0], 'alternatives') or
            len(response.results.channels[0].alternatives) == 0):
            logger.error("Deepgram response does not have the expected structure")
            raise Exception("Invalid response structure from Deepgram API")

        # Extract the transcript and other metadata
        transcript = response.results.channels[0].alternatives[0].transcript

        # Get additional metadata if available
        metadata = {}
        if hasattr(response, 'metadata'):
            if hasattr(response.metadata, 'duration'):
                metadata['duration_seconds'] = response.metadata.duration
            if hasattr(response.metadata, 'channels'):
                metadata['channels'] = response.metadata.channels

        # Process speaker segments if diarization was enabled
        speaker_segments = []
        if diarize and hasattr(response.results.channels[0].alternatives[0], 'words'):
            # Check if words attribute exists and is not empty
            if hasattr(response.results.channels[0].alternatives[0], 'words') and response.results.channels[0].alternatives[0].words:
                words = response.results.channels[0].alternatives[0].words
                speaker_segments = process_speaker_segments(words)

        # Convert dictionary speaker segments to SpeakerSegment objects for sentiment analysis
        speaker_segment_objects = []
        if speaker_segments:
            try:
                speaker_segment_objects = [
                    SpeakerSegment(speaker=segment['speaker'], text=segment['text'])
                    for segment in speaker_segments
                    if 'speaker' in segment and 'text' in segment and segment['text'].strip()
                ]
            except Exception as e:
                logger.error(f"Error converting speaker segments to objects: {str(e)}")
                # Continue with empty speaker segments rather than failing

        # Perform sentiment analysis if requested and diarization is enabled
        sentiment_analysis = None
        if analyze_sentiment and diarize and speaker_segment_objects and db:
            try:
                sentiment_analysis = analyze_conversation_sentiment(
                    db=db,
                    transcript=transcript,
                    speaker_segments=speaker_segment_objects
                )
            except Exception as e:
                logger.error(f"Error during sentiment analysis: {str(e)}")
                # Continue without sentiment analysis rather than failing
        elif analyze_sentiment and not db:
            logger.warning("Database session not provided, skipping sentiment analysis")

        result = {
            'text': transcript,
            'language': "multi",
            'metadata': metadata,
            'speaker_segments': speaker_segments
        }

        # Add sentiment analysis to result if available
        if sentiment_analysis:
            result['sentiment_analysis'] = sentiment_analysis.model_dump()

        return result

    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        raise Exception(f"Failed to transcribe audio: {str(e)}")


def process_speaker_segments(words: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """
    Process the words from Deepgram response to create speaker segments.

    Args:
        words: List of word objects from Deepgram response

    Returns:
        List of speaker segments with speaker ID and text
    """
    # Check if words list is empty or None
    if not words:
        logger.warning("No words found in the transcription for speaker segmentation")
        return []

    segments = []
    current_speaker = None
    current_text = []

    try:
        for word in words:
            # Extract speaker ID (e.g., 0, 1) and convert to string format (e.g., "spk_0", "spk_1")
            speaker = f"spk_{word.speaker}" if hasattr(word, 'speaker') else "unknown"

            # Get the word with punctuation
            if hasattr(word, 'punctuated_word') and word.punctuated_word:
                token = word.punctuated_word
            elif hasattr(word, 'word'):
                token = word.word
            else:
                # Skip this word if it doesn't have the expected attributes
                logger.warning(f"Word object missing required attributes: {word}")
                continue

            # Start a new segment when the speaker changes
            if speaker != current_speaker:
                if current_speaker is not None:
                    segments.append({
                        "speaker": current_speaker,
                        "text": " ".join(current_text).strip()
                    })
                current_speaker = speaker
                current_text = [token]
            else:
                current_text.append(token)

        # Append the last speaker segment
        if current_speaker is not None and current_text:
            segments.append({
                "speaker": current_speaker,
                "text": " ".join(current_text).strip()
            })
    except Exception as e:
        logger.error(f"Error processing speaker segments: {str(e)}")
        # Return an empty list if there's an error
        return []

    return segments
