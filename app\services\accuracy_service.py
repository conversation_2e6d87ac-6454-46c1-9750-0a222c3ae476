from openai import OpenAI, AzureOpenAI
from sqlalchemy.orm import Session
from app.core.settings import settings
from app.models.transcription import InformationAccuracy, SpeakerSegment, SentimentAnalysis
from app.services.school_info_service import get_school_info_by_category, get_all_school_info_for_ai
from typing import List, Dict, Optional
import json
import logging

logger = logging.getLogger(__name__)

def analyze_information_accuracy(
    db: Session,
    transcript: str,
    speaker_segments: List[SpeakerSegment],
    sentiment_analysis: SentimentAnalysis,
    transcription_id: str
) -> Optional[InformationAccuracy]:
    """
    Analyze the accuracy of information provided by university staff compared to category-specific school details.

    Args:
        db: Database session to retrieve school information
        transcript: The full transcript text
        speaker_segments: List of speaker segments from the diarized transcript
        sentiment_analysis: Sentiment analysis results containing the relevant category
        transcription_id: The ID of the transcription being analyzed

    Returns:
        InformationAccuracy object with analysis results or None if analysis fails
    """
    try:
        # Get the relevant category from sentiment analysis
        relevant_category = sentiment_analysis.relevant_category

        # Get category-specific school information
        school_details = ""
        if relevant_category and relevant_category != "general":
            # Try to get specific category information
            category_info = get_school_info_by_category(db, relevant_category)
            if category_info:
                school_details = f"=== {relevant_category.upper()} ===\n{category_info.content}"
                logger.info(f"Using category-specific information for '{relevant_category}' in accuracy analysis")
            else:
                logger.warning(f"Category '{relevant_category}' not found in database, using fallback strategy")
                # Fallback: use all available information
                school_info_dict = get_all_school_info_for_ai(db)
                school_details = format_school_info_for_ai(school_info_dict)
        else:
            # Use all available information for general conversations
            logger.info("Using all available school information for general conversation analysis")
            school_info_dict = get_all_school_info_for_ai(db)
            school_details = format_school_info_for_ai(school_info_dict)

        # Skip accuracy analysis if no school information is available
        if not school_details or school_details.strip() == "No school information available.":
            logger.warning("No school information available for accuracy analysis, skipping")
            return None

        # Initialize OpenAI client based on configuration
        if settings.USE_AZURE_OPENAI:
            client = AzureOpenAI(
                api_key=settings.AZURE_OPENAI_API_KEY,
                azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
                api_version=settings.AZURE_OPENAI_API_VERSION
            )
        else:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

        # Prepare conversation for analysis
        conversation_text = format_conversation_for_analysis(speaker_segments)

        # Create the prompt for OpenAI
        category_context = f" for the '{relevant_category}' category" if relevant_category and relevant_category != "general" else ""


        print(school_details)
        system_prompt = f"""
        # Identity
        You are an expert educational information auditor specializing in verifying the accuracy and completeness of information provided by university or college staff to students.

        # Task
        Analyze the provided conversation between a student and a university staff member. Compare the information provided by the staff against the official school details {category_context}. Your objective is to perform a **precise and context-aware evaluation** of the interaction. Determine the following:

        1. Base your analysis strictly on the school-related details provided. **Do not consider or infer from external sources** or make assumptions beyond what is explicitly included in the conversation and official data.
        2. Accuracy issues should only be flagged when the **university staff provides information that directly contradicts or misrepresents** the official school details.
        3. For each category below, apply this principle:

        ### a. Missed Information
        - Include only if the **student explicitly asked about something**, and the staff **failed to mention a relevant point** that was available in the official school details.
        - Do **not include missed points** for brief or closed questions where the intent was fully addressed.
        - Do not list unrelated or general details just because they exist in the school data — only capture information that was **clearly relevant and expected to be conveyed** given the student’s query.

        ### b. Incorrect Information
        - List only if the staff **explicitly provided a detail that is factually incorrect**, according to the official school details.
        - Do not assume something is incorrect unless there is a clear, factual mismatch between what the staff said and what the school data confirms.

        ### c. Incomplete Information
        - Include only if the staff **began answering a broader or complex question** (e.g., about a process, deadline, or eligibility), but **omitted important components** that were necessary to fully address the student’s intent.
        - Do not flag responses as incomplete if the conversation was brief or the student only asked a surface-level question.

        ### d. Correct Information
        - List all points where the staff member **accurately conveyed details** that are consistent with official school data.

       ### e. Overall Assessment & Accuracy Percentage
            - Summarize whether the staff gave factually sound and contextually complete answers.
            - Apply the following accuracy logic:

            **Set "accuracy_percentage" to "NA" if:**
            - The conversation was short, general, or purely logistical (e.g., greetings, rescheduling).
            - The student **did not ask for any information** (e.g., only confirming, making a statement, or asking non-informational questions).
            - There was **no exchange of school-related content**.
            - Accuracy simply doesn’t apply to the nature of the exchange (e.g., student was redirected, issue was non-academic).

            **Set accuracy to 100% only if:**
            - The student **clearly asked one or more informational questions**.
            - The staff provided **only factually correct** responses based on official school details.

            **Lower the accuracy only if:**
            - The staff explicitly provided **verifiably incorrect** information.
            - **Missed or incomplete points should be flagged separately**, but **do not affect the accuracy score**.
            - Incorrect information should impact accuracy percentage and lower it's value

        ### f. Conversation Flags
        Analyze the student's tone and emotional cues in the conversation. Indicate the presence of any of the following:
        - **Satisfaction** – Student explicitly expresses gratitude, appreciation, or positive feedback about the help received (e.g., "thank you so much", "that's very helpful", "perfect")
        - **Confusion** – Student shows clear signs of not understanding:
                        - Asks "what do you mean?" or "I don't understand"
                        - Repeats the same question multiple times
                        - Asks for clarification on basic concepts
                        - NOT for normal follow-up questions
        - **Urgency** – Mark as true only if student explicitly mentions:
                    - Time-sensitive deadlines (e.g., "application closes tomorrow", "need to decide by Friday")
                    - Immediate processing requests (e.g., "can you fast-track this", "need this urgently", "ASAP")
                    - Emergency situations (e.g., "visa expires soon", "missed deadline")
                    - NOT for general inquiries about deadlines or timelines
        - **Frustration** – Student demonstrates clear irritation:
                        - Uses words like "frustrated", "annoyed", "disappointed"
                        - Mentions previous unsuccessful attempts
                        - Expresses dissatisfaction with processes or responses
                        - Shows impatience with explanations
        - **Abusive** – Student uses inappropriate language:
                        - Profanity, insults, or threatening language
                        - Disrespectful tone toward staff
                        - Harassment or inappropriate demands

        ### g. Conversation Tone
        Assess the overall tone of the university assistant during the interaction:
        This assessment focuses solely on the university assistant's communication style, not the student's reactions:

        - **professional** – Maintains appropriate formality, follows protocols, courteous but businesslike
        - **warm** – Friendly, empathetic, welcoming, creates personal connection
        - **enthusiastic** – Shows genuine excitement about helping, energetic, goes beyond basic requirements
        - **neutral** – Provides information factually without particular emotional engagement
        - **dismissive** – Provides minimal responses, seems eager to end conversation quickly
        - **impatient** – Shows signs of rushing, interrupting, or giving abbreviated responses

        ### h. Decision Logic
        - **Do not flag missed or incomplete information** unless the student explicitly asked about a topic that warranted a more complete or accurate response.
        - **Always examine the student’s intent** behind the question before determining missed or incomplete information.
        - Short, resolved conversations should not result in accuracy deductions unless incorrect information was actually stated.

        **Note:** This analysis is focused on {relevant_category if relevant_category and relevant_category != "general" else "general school"} information.

        #Response
        Format your response as a JSON object with the following structure:
        {{
            "missed_information": [
                "Point 1 that was missed",
                "Point 2 that was missed"
            ],
            "incorrect_information": [
                {{
                    "provided": "What the staff incorrectly said",
                    "correct": "What the correct information is"
                }}
            ],
            "incomplete_information": [
                {{
                    "provided": "What the staff incompletely said",
                    "complete": "What the complete information is"
                }}
            ],
            "correct_information": [
                "Point 1 that was correctly provided",
                "Point 2 that was correctly provided"
            ],
            "overall_assessment": "A brief assessment of the overall accuracy of the information provided",
            "accuracy_percentage": 85.5,// Or "NA" if conversation was not informational in nature
            "conversation_flags": {{
                "satisfaction": true,
                "confusion": false,
                "urgency": true,
                "frustration": false,
                "abusive": false
            }},
            "conversation_tone": "professional/warm/enthusiastic/neutral/dismissive/impatient"
        }}

        Only include information that was explicitly discussed in the conversation. Do not list all school details
        as missed information if they weren't relevant to the conversation topic.
"""

        user_prompt = f"""
        Here is the conversation to analyze:

        {conversation_text}

        Here are the official school details to compare against:

        {school_details}

        Please analyze this conversation and provide your assessment in the JSON format specified.
        """

        # Call OpenAI API
        model_name = settings.AZURE_OPENAI_DEPLOYMENT if settings.USE_AZURE_OPENAI else settings.OPENAI_MODEL
        response = client.chat.completions.create(
            model=model_name,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2,  # Lower temperature for more consistent results
        )

        # Extract and parse the response
        analysis_json = json.loads(response.choices[0].message.content)

        # Convert the JSON response to an InformationAccuracy object
        information_accuracy = InformationAccuracy(
            missed_information=analysis_json.get("missed_information", []),
            incorrect_information=analysis_json.get("incorrect_information", []),
            incomplete_information=analysis_json.get("incomplete_information", []),
            correct_information=analysis_json.get("correct_information", []),
            overall_assessment=analysis_json.get("overall_assessment", ""),
            accuracy_percentage=analysis_json.get("accuracy_percentage", 0.0),
            conversation_flags=analysis_json.get("conversation_flags", {
                "satisfaction": False,
                "confusion": False,
                "urgency": False,
                "frustration": False,
                "abusive": False
            }),
            conversation_tone=analysis_json.get("conversation_tone", None)
        )

        return information_accuracy

    except Exception as e:
        logger.error(f"Information accuracy analysis error: {str(e)}")
        return None


def format_conversation_for_analysis(speaker_segments: List[SpeakerSegment]) -> str:
    """
    Format the speaker segments into a readable conversation format for analysis.

    Args:
        speaker_segments: List of speaker segments from the diarized transcript

    Returns:
        Formatted conversation text
    """
    if not speaker_segments:
        return ""

    conversation_lines = []

    for segment in speaker_segments:
        conversation_lines.append(f"{segment.speaker}: {segment.text}")

    return "\n".join(conversation_lines)


def format_school_info_for_ai(school_info_dict: Dict[str, str]) -> str:
    """
    Format school information dictionary into a readable format for AI analysis.

    Args:
        school_info_dict: Dictionary with category as key and content as value

    Returns:
        Formatted school information text
    """
    if not school_info_dict:
        return "No school information available."

    formatted_sections = []

    for category, content in school_info_dict.items():
        formatted_sections.append(f"=== {category.upper()} ===\n{content}")

    return "\n\n".join(formatted_sections)
