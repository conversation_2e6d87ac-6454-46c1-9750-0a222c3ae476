from fastapi import H<PERSON>PException
import httpx
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, <PERSON>ple
import io
import asyncio
from concurrent.futures import ThreadPoolExecutor

from app.core.settings import settings
from app.models.call_data import CallDetails, CallDataResponse, ProcessedCallData
from app.services.transcription_service import transcribe_audio_file
from app.models.transcription import TranscriptionResponse, SpeakerSegment, SentimentAnalysis
from app.services.db_service import (
    save_transcription,
    save_call_data,
    get_call_data_by_call_id,
    save_transcription_failure,
    save_information_accuracy,
)
import uuid
from datetime import datetime
from app.services.accuracy_service import analyze_information_accuracy
import json
import re

logger = logging.getLogger(__name__)
logger.disabled=False

MIN_CALL_DURATION_SECONDS = 10  # Minimum call duration to process (in seconds)


async def fetch_call_data(start_date: str, end_date: str) -> CallDataResponse:
    """
    Fetch call data from the MCube API.

    Args:
        start_date: Start date in format 'YYYY-MM-DD HH:MM:SS'
        end_date: End date in format 'YYYY-MM-DD HH:MM:SS'

    Returns:
        CallDataResponse: The API response with call data

    Raises:
        Exception: If the API request fails
    """
    try:
        # Prepare the request payload
        payload = {
            "start_date": start_date,
            "end_date": end_date,
            "Authorization": settings.MCUBE_API_AUTH_TOKEN
        }

        # Make the API request
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                settings.MCUBE_API_URL,
                json=payload
            )

            # Check if the request was successful
            response.raise_for_status()

            # Parse the response
            data = response.json()
            return CallDataResponse(**data)

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error occurred: {e}")
        raise Exception(f"Failed to fetch call data: HTTP {e.response.status_code}")
    except httpx.RequestError as e:
        logger.error(f"Request error occurred: {e}")
        raise Exception(f"Failed to fetch call data: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching call data: {str(e)}")
        raise Exception(f"Failed to fetch call data: {str(e)}")


def filter_valid_calls(calls: List[Dict[str, Any]]) -> List[CallDetails]:
    """
    Filter calls based on duration and other criteria.

    Args:
        calls: List of call data dictionaries

    Returns:
        List of CallDetails objects for valid calls
    """
    valid_calls = []

    for call in calls:
        try:
            call_duration = 0
            callid = call.get("callid", "unknown")

            # Parse onhangupRequest to get call_duration
            if call.get("onhangupRequest"):
                request_text = call["onhangupRequest"]
                request_match = re.search(r"Request:(\{.*\})$", request_text)

                if request_match:
                    try:
                        hangup_data = json.loads(request_match.group(1))
                        hangup_call_duration = hangup_data.get("call_duration", 0)

                        # Convert to int
                        if (
                            isinstance(hangup_call_duration, str)
                            and hangup_call_duration.isdigit()
                        ):
                            call_duration = int(hangup_call_duration)
                        else:
                            call_duration = (
                                int(hangup_call_duration) if hangup_call_duration else 0
                            )

                    except json.JSONDecodeError:
                        pass

            # Check if call meets criteria
            has_audio = call.get("filename", "").endswith(".wav")

            if call_duration >= MIN_CALL_DURATION_SECONDS and has_audio:
                call_details = CallDetails(**call)
                call_details.call_duration = call_duration
                valid_calls.append(call_details)
                logger.info(f"Valid call {callid}: duration={call_duration}s")
            else:
                logger.info(
                    f"Invalid call {callid}: duration={call_duration}s, has_audio={has_audio}"
                )

        except Exception as e:
            logger.error(
                f"Error processing call {call.get('callid', 'unknown')}: {str(e)}"
            )
            continue

    logger.info(
        f"Filtered {len(valid_calls)} valid calls out of {len(calls)} total calls"
    )
    return valid_calls


async def download_audio_file(url: str) -> Optional[bytes]:
    """
    Download an audio file from a URL.

    Args:
        url: URL of the audio file

    Returns:
        bytes: The audio file content or None if download fails
    """
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(url)
            response.raise_for_status()
            return response.content
    except Exception as e:
        logger.error(f"Error downloading audio file from {url}: {str(e)}")
        return None


async def process_call_for_transcription(
    call: CallDetails,
    db
) -> Tuple[ProcessedCallData, Optional[TranscriptionResponse]]:
    """
    Process a call for transcription.

    Args:
        call: Call details
        db: Database session

    Returns:
        Tuple of ProcessedCallData and TranscriptionResponse (or None if transcription fails)
    """
    # Initialize processed call data with additional fields
    processed_call = ProcessedCallData(
        call_id=call.callid,
        call_duration=call.call_duration,
        audio_url=call.filename,
        agent_name=call.agentname,
        group_name=call.groupname,
        call_direction=call.direction,
        call_source=call.source
    )

    # Extract date and time from starttime if available
    if call.starttime:
        try:
            start_datetime = datetime.strptime(call.starttime, "%Y-%m-%d %H:%M:%S")
            processed_call.call_date = start_datetime.strftime("%Y-%m-%d")
            processed_call.call_time = start_datetime.strftime("%H:%M:%S")
        except (ValueError, TypeError):
            logger.warning(f"Could not parse start time for call {call.callid}")

    # Try to extract student information from various fields
    # This is a placeholder - you may need to adjust based on where student info is actually stored
    if hasattr(call, "caller_email") and call.caller_email:
        processed_call.student_id = call.caller_email

    if hasattr(call, "callername") and call.callername:
        processed_call.student_name = call.callername

    # Download the audio file
    if call.filename:
        audio_content = await download_audio_file(call.filename)
        if audio_content:
            try:
                # Transcribe the audio
                transcription_result = transcribe_audio_file(
                    file_content=audio_content,
                    db=db,
                    smart_format=True,
                    punctuate=True,
                    paragraphs=True,
                    diarize=True,
                    profanity_filter=True,
                    analyze_sentiment=True
                )

                # Extract speaker segments if available
                speaker_segments = []
                if 'speaker_segments' in transcription_result:
                    for segment in transcription_result['speaker_segments']:
                        speaker_segments.append(SpeakerSegment(
                            speaker=segment['speaker'],
                            text=segment['text']
                        ))

                # Extract sentiment analysis if available
                sentiment_analysis = None
                if 'sentiment_analysis' in transcription_result:
                    sentiment_analysis = SentimentAnalysis.model_validate(
                        transcription_result['sentiment_analysis']
                    )

                # # Extract duration from metadata if available
                # duration_seconds = 0.0
                # if 'metadata' in transcription_result and 'duration_seconds' in transcription_result['metadata']:
                #     duration_seconds = transcription_result['metadata']['duration_seconds']

                # Create the response object
                transcription_response = TranscriptionResponse(
                    id=str(uuid.uuid4()),
                    text=transcription_result['text'],
                    metadata=transcription_result.get('metadata', {}),
                    created_at=datetime.now(),
                    speaker_segments=speaker_segments,
                    sentiment_analysis=sentiment_analysis
                )

                # Save to database
                filename = f"mcube_call_{call.callid}"
                db_id = save_transcription(
                    db=db,
                    filename=filename,
                    transcription_response=transcription_response
                )

                # Update the response ID with the database ID
                transcription_response.id = db_id

                # Update processed call data
                processed_call.transcription_id = db_id
                processed_call.processed = True

                # Save call data to database
                call_data_id = save_call_data(db, processed_call)
                logger.info(f"Call data saved to database with ID: {call_data_id}")

                logger.info(f"Performing new information accuracy analysis for call")
                information_accuracy = analyze_information_accuracy(
                    db=db,
                    transcript=transcription_response.text,
                    speaker_segments=transcription_response.speaker_segments,
                    sentiment_analysis=transcription_response.sentiment_analysis,
                    transcription_id=transcription_response.id,
                )

                if not information_accuracy:
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to analyze information accuracy"
                    )

                # Save to database
                try:
                    save_information_accuracy(
                        db=db,
                        transcription_id=transcription_response.id,
                        information_accuracy=information_accuracy,
                    )
                    db.commit()
                    logger.info(f"Information accuracy analysis saved for call")
                except Exception as e:
                    db.rollback()
                    logger.error(f"Failed to save information accuracy analysis: {str(e)}")
                    # Continue with the response even if database save fails

                return processed_call, transcription_response

            except Exception as e:
                error_message = f"Transcription failed: {str(e)}"
                logger.error(f"Transcription failed for call {call.callid}: {str(e)}")

                # Save the failure record
                try:
                    save_transcription_failure(
                        db=db,
                        call_id=call.callid,
                        error_message=error_message,
                        start_date=call.starttime,
                        end_date=call.endtime,
                        audio_url=call.filename
                    )
                    logger.info(f"Saved failure record for call {call.callid}")
                except Exception as save_error:
                    logger.error(f"Failed to save transcription failure record: {str(save_error)}")

                # Return without saving call data when transcription fails
                return processed_call, None

    # If we reach here, it means either there's no filename or audio content couldn't be downloaded
    error_message = "No audio content available for processing"
    logger.warning(f"No audio content available for call {call.callid}")

    # Save the failure record
    try:
        save_transcription_failure(
            db=db,
            call_id=call.callid,
            error_message=error_message,
            start_date=call.starttime,
            end_date=call.endtime,
            audio_url=call.filename
        )
        logger.info(f"Saved failure record for call {call.callid} (no audio content)")
    except Exception as save_error:
        logger.error(f"Failed to save transcription failure record: {str(save_error)}")

    return processed_call, None


async def process_calls_concurrently(
    calls: List[CallDetails],
    db,
    max_concurrent_tasks: int = 5
) -> List[ProcessedCallData]:
    """
    Process multiple calls concurrently using asyncio.

    Args:
        calls: List of call details to process
        db: Database session
        max_concurrent_tasks: Maximum number of concurrent tasks (default: 5)

    Returns:
        List of successfully processed call data
    """
    processed_calls = []

    # Process calls in batches to limit concurrency
    for i in range(0, len(calls), max_concurrent_tasks):
        batch = calls[i:i + max_concurrent_tasks]
        # Create tasks for concurrent processing
        tasks = []
        for call in batch:
            # Check if call data already exists before creating a task
            existing_call_data = get_call_data_by_call_id(db, call.callid)

            if existing_call_data and existing_call_data.processed and existing_call_data.transcription_id:
                logger.info(f"Call {call.callid} already processed with transcription ID {existing_call_data.transcription_id}")
                processed_calls.append(existing_call_data)
                continue

            # Create a task for processing this call
            logger.info(f"Creating task for processing call {call.callid}")

            task = asyncio.create_task(process_call_for_transcription(call, db))
            tasks.append(task)

        if not tasks:
            # No tasks to process in this batch
            continue

        # Process the batch concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        # Process results
        for result in results:
            if isinstance(result, Exception):
                # Handle exceptions from tasks
                logger.error(f"Error in concurrent processing: {str(result)}")
                continue

            # Unpack the result tuple
            processed_call, transcription = result

            # Only add successfully transcribed calls
            if transcription:
                processed_calls.append(processed_call)

    return processed_calls
