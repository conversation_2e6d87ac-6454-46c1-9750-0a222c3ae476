from openai import OpenAI, AzureOpenAI
from sqlalchemy.orm import Session
from app.core.settings import settings
from app.models.transcription import (
    SentimentAnalysis, SentimentLevel, ConversationRole,
    SpeakerSegment
)
from app.services.school_info_service import get_all_school_info_for_ai
from typing import List, Dict, Any, Optional
import json
import logging

logger = logging.getLogger(__name__)

def analyze_conversation_sentiment(
    db: Session,
    transcript: str,
    speaker_segments: List[SpeakerSegment]
) -> Optional[SentimentAnalysis]:
    """
    Analyze the sentiment of a conversation using OpenAI with category identification.

    Args:
        db: Database session to retrieve school information categories
        transcript: The full transcript text
        speaker_segments: List of speaker segments from the diarized transcript

    Returns:
        SentimentAnalysis object with sentiment analysis results or None if analysis fails
    """
    try:
        # Get available school information categories
        school_info_dict = get_all_school_info_for_ai(db)
        available_categories = list(school_info_dict.keys()) if school_info_dict else []

        # Initialize OpenAI client based on configuration
        if settings.USE_AZURE_OPENAI:
            client = AzureOpenAI(
                api_key=settings.AZURE_OPENAI_API_KEY,
                azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
                api_version=settings.AZURE_OPENAI_API_VERSION
            )
        else:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)

        # Prepare conversation for analysis
        conversation_text = format_conversation_for_analysis(speaker_segments)

        # Create the prompt for OpenAI
        categories_text = ", ".join([f'"{cat}"' for cat in available_categories]) if available_categories else "No categories available"

        system_prompt = f"""
            #Identity
            As a sentiment analyst you are needed to review the provided transcript and provide required information about the same to the user. You have vast experience in dealing with educational institute conversations between councillors and students.

            #Task
            1.Validate if this appears to be a conversation between a student and a university/college assistant or staff member.
            2. The overall sentiment of the conversation based on the transcript(very negative, negative, neutral, positive, very positive).
            3. The most relevant school information category for this conversation from the available categories: {categories_text}
            4. Key points from the conversation (3-5 bullet points less than 15 words).
            5. For each speaker, determine their likely role (student, university assistant, or other).
            6. A brief summary of the conversation (100 words).
            7. Suggested action items based on the conversation (if any, less than 10 words).

            #Guidelines
            - Be very critical about each statement provided in the transcript.
            - The transcript might say weird names but the university is IILM University. Always ensure you use this name, due to recording environment and noise you might read "Ireland" or "i am" university. But keep your responses for IILM university only.
            - Students will refer to examinations they have appeared for like JEE, CET, etc. Ensure we use correct names for them

            #Response
            Format your response as a JSON object with the following structure:
            {{
                "is_student_university_conversation": boolean,
                "overall_sentiment": "very_negative" | "negative" | "neutral" | "positive" | "very_positive",
                "relevant_category": "category_name" | "general",
                "key_points": ["point1", "point2", "point3"],
                "speaker_roles": {{
                    "spk_0": "student" | "university_assistant" | "other" | "unknown",
                    "spk_1": "student" | "university_assistant" | "other" | "unknown"
                }},
                "summary": "Brief summary of the conversation",
                "action_items": ["action1", "action2"]
            }}
            """

        user_prompt = f"""
        Here is the conversation to analyze:

        {conversation_text}

        Please analyze this conversation and provide your assessment in the JSON format specified.
        """

        # Call OpenAI API
        model_name = settings.AZURE_OPENAI_DEPLOYMENT if settings.USE_AZURE_OPENAI else settings.OPENAI_MODEL
        response = client.chat.completions.create(
            model=model_name,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2,  # Lower temperature for more consistent results
        )

        # Extract and parse the response
        analysis_json = json.loads(response.choices[0].message.content)

        # Validate and clean the relevant_category
        relevant_category = analysis_json.get("relevant_category", "general")
        if relevant_category not in available_categories and relevant_category != "general":
            logger.warning(f"AI returned invalid category '{relevant_category}', using 'general' instead")
            relevant_category = "general"

        # Convert the JSON response to a SentimentAnalysis object
        sentiment_analysis = SentimentAnalysis(
            is_student_university_conversation=analysis_json.get("is_student_university_conversation", False),
            overall_sentiment=analysis_json.get("overall_sentiment", SentimentLevel.NEUTRAL),
            relevant_category=relevant_category,
            key_points=analysis_json.get("key_points", []),
            speaker_roles={k: v for k, v in analysis_json.get("speaker_roles", {}).items()},
            summary=analysis_json.get("summary", ""),
            action_items=analysis_json.get("action_items", [])
        )

        return sentiment_analysis

    except Exception as e:
        logger.error(f"Sentiment analysis error: {str(e)}")
        return None


def format_conversation_for_analysis(speaker_segments: List[SpeakerSegment]) -> str:
    """
    Format the speaker segments into a readable conversation format for analysis.

    Args:
        speaker_segments: List of speaker segments from the diarized transcript

    Returns:
        Formatted conversation text
    """
    if not speaker_segments:
        return ""

    conversation_lines = []

    for segment in speaker_segments:
        conversation_lines.append(f"{segment.speaker}: {segment.text}")

    return "\n".join(conversation_lines)
