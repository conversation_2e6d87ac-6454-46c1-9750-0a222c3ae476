from sqlalchemy.orm import Session
from sqlalchemy import func, case, cast, Float, and_, or_
from app.db.models import (
    CallData, Transcription, SentimentAnalysisDB, InformationAccuracyDB,
    ConversationFlagDB, ConversationToneDB, SentimentEnum, ConversationFlagEnum, ConversationToneEnum
)
from app.models.analytics import CallAnalytics, SentimentDistribution, EmotionalFlagCounts, ConversationToneCounts
from app.models.filters import CallFilters, FilterType
import logging
from typing import Dict, Any, List, Optional
from datetime import date, timedelta

logger = logging.getLogger(__name__)


def _build_analytics_filter_conditions(filters: Optional[CallFilters]):
    """
    Build SQLAlchemy filter conditions for analytics queries.

    Args:
        filters: CallFilters object containing filtering parameters

    Returns:
        Tuple of (base_conditions, sentiment_conditions, flag_conditions)
    """
    base_conditions = []
    sentiment_conditions = []
    flag_conditions = []

    if not filters:
        return base_conditions, sentiment_conditions, flag_conditions

    # Date filtering
    if filters.filter_type != FilterType.ALL:
        today = date.today()

        if filters.filter_type == FilterType.TODAY:
            target_date = today.strftime('%Y-%m-%d')
            base_conditions.append(CallData.call_date == target_date)

        elif filters.filter_type == FilterType.YESTERDAY:
            yesterday = today - timedelta(days=1)
            target_date = yesterday.strftime('%Y-%m-%d')
            base_conditions.append(CallData.call_date == target_date)

        elif filters.filter_type == FilterType.SPECIFIC_DAY and filters.specific_date:
            target_date = filters.specific_date.strftime('%Y-%m-%d')
            base_conditions.append(CallData.call_date == target_date)

        elif filters.filter_type == FilterType.DATE_RANGE and filters.start_date and filters.end_date:
            start_date_str = filters.start_date.strftime('%Y-%m-%d')
            end_date_str = filters.end_date.strftime('%Y-%m-%d')
            base_conditions.append(and_(
                CallData.call_date >= start_date_str,
                CallData.call_date <= end_date_str
            ))

    # Sentiment filtering
    if filters.sentiment_filter:
        # Convert filter values to enum values for better filtering
        sentiment_enum_values = []
        for s in filters.sentiment_filter:
            if hasattr(s, 'value'):
                # It's an enum object, get the string value
                filter_value = s.value
            else:
                # It's already a string
                filter_value = s

            # Convert to SentimentEnum
            try:
                sentiment_enum_values.append(SentimentEnum(filter_value))
            except ValueError:
                logger.warning(f"Invalid sentiment filter value: {filter_value}")
                continue

        if sentiment_enum_values:
            sentiment_conditions.append(SentimentAnalysisDB.overall_sentiment.in_(sentiment_enum_values))

    # Conversation flags filtering
    if filters.conversation_flags:
        # Convert filter values to enum values for better filtering
        flag_enum_values = []
        for flag in filters.conversation_flags:
            if hasattr(flag, 'value'):
                # It's an enum object, get the string value
                filter_value = flag.value
            else:
                # It's already a string
                filter_value = flag

            # Convert to ConversationFlagEnum (ensure lowercase)
            try:
                filter_value_lower = filter_value.lower()
                flag_enum_values.append(ConversationFlagEnum(filter_value_lower))
            except ValueError:
                logger.warning(f"Invalid conversation flag filter value: {filter_value}")
                continue

        if flag_enum_values:
            # Use the new ConversationFlagDB table for filtering
            # Filter for records where the flag is present (is_present = True)
            flag_conditions.append(
                and_(
                    ConversationFlagDB.flag_type.in_(flag_enum_values),
                    ConversationFlagDB.is_present == True
                )
            )

    return base_conditions, sentiment_conditions, flag_conditions


def get_call_analytics(db: Session, filters: Optional[CallFilters] = None) -> CallAnalytics:
    """
    Generate analytics for processed calls in the database, with optional date filtering only.

    Args:
        db: Database session
        filters: Optional CallFilters object for date filtering only

    Returns:
        CallAnalytics: Object containing comprehensive analytics metrics
    """
    try:
        # Initialize the analytics object
        analytics = CallAnalytics()

        # Build date filter conditions only (ignore sentiment and flag filters for analytics)
        date_conditions = []

        if filters and filters.filter_type != FilterType.ALL:
            today = date.today()

            if filters.filter_type == FilterType.TODAY:
                target_date = today.strftime('%Y-%m-%d')
                date_conditions.append(CallData.call_date == target_date)

            elif filters.filter_type == FilterType.YESTERDAY:
                yesterday = today - timedelta(days=1)
                target_date = yesterday.strftime('%Y-%m-%d')
                date_conditions.append(CallData.call_date == target_date)

            elif filters.filter_type == FilterType.SPECIFIC_DAY and filters.specific_date:
                target_date = filters.specific_date.strftime('%Y-%m-%d')
                date_conditions.append(CallData.call_date == target_date)

            elif filters.filter_type == FilterType.DATE_RANGE and filters.start_date and filters.end_date:
                start_date_str = filters.start_date.strftime('%Y-%m-%d')
                end_date_str = filters.end_date.strftime('%Y-%m-%d')
                date_conditions.append(and_(
                    CallData.call_date >= start_date_str,
                    CallData.call_date <= end_date_str
                ))

        # Build base query for call data with date filtering only
        call_query = db.query(CallData)
        for condition in date_conditions:
            call_query = call_query.filter(condition)

        # Get total number of calls (with date filters applied)
        total_calls = call_query.count()
        analytics.total_calls = total_calls

        if total_calls == 0:
            # No calls to analyze
            return analytics

        # Get average call duration (with date filters applied)
        avg_duration = call_query.with_entities(func.avg(CallData.call_duration)).scalar() or 0
        analytics.average_duration = float(avg_duration)

        # Build query for information accuracy with date filtering only
        accuracy_query = db.query(InformationAccuracyDB).join(
            Transcription, InformationAccuracyDB.transcription_id == Transcription.id
        ).join(
            CallData, Transcription.id == CallData.transcription_id
        )

        # Apply date filter conditions to accuracy query
        for condition in date_conditions:
            accuracy_query = accuracy_query.filter(condition)

        # Get average information accuracy (with date filters applied)
        # Only include numeric values, exclude any string values (NA, na, etc.)
        try:
            # Get all accuracy percentages
            accuracy_values = accuracy_query.with_entities(
                InformationAccuracyDB.accuracy_percentage
            ).filter(
                InformationAccuracyDB.accuracy_percentage.isnot(None)
            ).all()

            # Convert to float and calculate average, excluding any non-numeric values
            numeric_accuracies = []
            for (value,) in accuracy_values:
                if value is not None:
                    try:
                        # Try to convert to float - this will fail for any string
                        numeric_value = float(value)
                        # Additional check to ensure it's a reasonable percentage (0-100)
                        if 0 <= numeric_value <= 100:
                            numeric_accuracies.append(numeric_value)
                    except (ValueError, TypeError):
                        # Skip any non-numeric values (including "NA", "na", etc.)
                        logger.debug(f"Skipping non-numeric accuracy value: {value}")
                        continue

            avg_accuracy = sum(numeric_accuracies) / len(numeric_accuracies) if numeric_accuracies else 0
            analytics.average_accuracy = float(avg_accuracy)
            logger.info(f"Calculated average accuracy from {len(numeric_accuracies)} numeric values: {avg_accuracy:.2f}")
        except Exception as e:
            logger.error(f"Error calculating average accuracy: {str(e)}")
            analytics.average_accuracy = 0.0

        # Initialize sentiment distribution
        sentiment_distribution = SentimentDistribution()

        try:
            # Build query for sentiment analysis with date filtering only
            sentiment_query = db.query(SentimentAnalysisDB).join(
                Transcription, SentimentAnalysisDB.transcription_id == Transcription.id
            ).join(
                CallData, Transcription.id == CallData.transcription_id
            )

            # Apply date filter conditions to sentiment query
            for condition in date_conditions:
                sentiment_query = sentiment_query.filter(condition)

            # Get sentiment distribution using SQL query with filters
            sentiment_counts = sentiment_query.with_entities(
                SentimentAnalysisDB.overall_sentiment,
                func.count(SentimentAnalysisDB.id)
            ).group_by(
                SentimentAnalysisDB.overall_sentiment
            ).all()

            # Log all sentiment values found in the database
            logger.info(f"Date-filtered sentiment values in database: {sentiment_counts}")

            # Count of transcriptions with sentiment analysis
            total_with_sentiment = sum(count for _, count in sentiment_counts)

            # Process sentiment counts
            for sentiment, count in sentiment_counts:
                # In the database, sentiment values are now stored as enum objects
                if sentiment:
                    sentiment_value = sentiment.value if hasattr(sentiment, 'value') else str(sentiment)
                    if sentiment_value.lower() in ["positive", "very_positive"]:
                        sentiment_distribution.positive_count += count
                    elif sentiment_value.lower() in ["negative", "very_negative"]:
                        sentiment_distribution.negative_count += count
                    elif sentiment_value.lower() == "neutral":
                        sentiment_distribution.neutral_count += count

                    # Log the sentiment values found in the database for debugging
                    logger.info(f"Found date-filtered sentiment value in database: {sentiment_value} with count {count}")

        except Exception as e:
            logger.error(f"Error querying date-filtered sentiment distribution: {str(e)}")

            # Try a different approach: fetch all date-filtered records and count manually
            try:
                # Get all sentiment analysis records with date filters applied
                sentiment_records = sentiment_query.all()

                # Initialize counters
                positive_count = 0
                negative_count = 0
                neutral_count = 0

                # Count each sentiment manually
                for record in sentiment_records:
                    if record.overall_sentiment:
                        sentiment = record.overall_sentiment.lower()
                        if sentiment in ["positive", "very_positive"]:
                            positive_count += 1
                        elif sentiment in ["negative", "very_negative"]:
                            negative_count += 1
                        elif sentiment == "neutral":
                            neutral_count += 1

                # Set the counts
                sentiment_distribution.positive_count = positive_count
                sentiment_distribution.negative_count = negative_count
                sentiment_distribution.neutral_count = neutral_count

                # Recalculate total
                total_with_sentiment = positive_count + negative_count + neutral_count

                # Log the manual counts
                logger.info(f"Manual date-filtered sentiment counts: positive={positive_count}, negative={negative_count}, neutral={neutral_count}")

            except Exception as e2:
                logger.error(f"Error manually counting date-filtered sentiments: {str(e2)}")

        # Calculate percentages if we have sentiment data
        if total_with_sentiment > 0:
            sentiment_distribution.positive_percentage = (
                sentiment_distribution.positive_count / total_with_sentiment * 100
            )
            sentiment_distribution.negative_percentage = (
                sentiment_distribution.negative_count / total_with_sentiment * 100
            )
            sentiment_distribution.neutral_percentage = (
                sentiment_distribution.neutral_count / total_with_sentiment * 100
            )

        # Set positive sentiment percentage in main analytics
        analytics.positive_sentiment_percentage = sentiment_distribution.positive_percentage

        # Set sentiment distribution in analytics
        analytics.sentiment_distribution = sentiment_distribution

        # Get emotional flag counts using the new ConversationFlagDB table (with date filters applied)
        emotional_flags = EmotionalFlagCounts()

        try:
            # Build query for conversation flags with date filtering only
            flag_query = db.query(ConversationFlagDB).join(
                Transcription, ConversationFlagDB.transcription_id == Transcription.id
            ).join(
                CallData, Transcription.id == CallData.transcription_id
            )

            # Apply date filter conditions to flag query
            for condition in date_conditions:
                flag_query = flag_query.filter(condition)

            # Get flag counts using SQL query with filters
            # Only count flags that are present (is_present = True)
            flag_counts_query = flag_query.filter(
                ConversationFlagDB.is_present == True
            ).with_entities(
                ConversationFlagDB.flag_type,
                func.count(ConversationFlagDB.id)
            ).group_by(
                ConversationFlagDB.flag_type
            ).all()

            # Initialize counters for each flag
            flag_counts = {
                "satisfaction": 0,
                "confusion": 0,
                "urgency": 0,
                "frustration": 0,
                "abusive": 0
            }

            # Process flag counts
            for flag_type, count in flag_counts_query:
                if flag_type:
                    flag_name = flag_type.value if hasattr(flag_type, 'value') else str(flag_type)
                    if flag_name in flag_counts:
                        flag_counts[flag_name] = count

            # Log the counts
            logger.info(f"Date-filtered emotional flag counts: {flag_counts}")

            # Set the counts in the emotional flags object
            for flag, count in flag_counts.items():
                setattr(emotional_flags, flag, count)

        except Exception as e:
            logger.error(f"Error counting date-filtered emotional flags: {str(e)}")

            # Set all counts to 0 as fallback
            for flag in ["satisfaction", "confusion", "urgency", "frustration", "abusive"]:
                setattr(emotional_flags, flag, 0)

        # Set emotional flags in analytics
        analytics.emotional_flags = emotional_flags

        # Get conversation tone counts using the ConversationToneDB table (with date filters applied)
        conversation_tones = ConversationToneCounts()

        try:
            # Build query for conversation tones with date filtering
            tone_query = db.query(ConversationToneDB).join(
                Transcription, ConversationToneDB.transcription_id == Transcription.id
            ).join(
                CallData, Transcription.id == CallData.transcription_id
            )

            # Apply date filter conditions to tone query
            for condition in date_conditions:
                tone_query = tone_query.filter(condition)

            # Get tone counts using SQL query with filters
            tone_counts_query = tone_query.with_entities(
                ConversationToneDB.tone,
                func.count(ConversationToneDB.id)
            ).group_by(
                ConversationToneDB.tone
            ).all()

            # Initialize counters for each tone
            tone_counts = {
                "pleasant": 0,
                "dull": 0,
                "unanalyzed": 0
            }

            # Process tone counts
            for tone_type, count in tone_counts_query:
                if tone_type:
                    tone_name = tone_type.value if hasattr(tone_type, 'value') else str(tone_type)
                    if tone_name in tone_counts:
                        tone_counts[tone_name] = count
                else:
                    # Count records with None tone as unanalyzed
                    tone_counts["unanalyzed"] = count

            # Log the counts
            logger.info(f"Date-filtered conversation tone counts: {tone_counts}")

            # Set the counts in the conversation tones object
            for tone, count in tone_counts.items():
                setattr(conversation_tones, tone, count)

        except Exception as e:
            logger.error(f"Error counting date-filtered conversation tones: {str(e)}")

            # Set all counts to 0 as fallback
            for tone in ["pleasant", "dull", "unanalyzed"]:
                setattr(conversation_tones, tone, 0)

        # Set conversation tones in analytics
        analytics.conversation_tones = conversation_tones

        return analytics

    except Exception as e:
        logger.error(f"Error generating call analytics: {str(e)}")
        # Return default analytics object in case of error
        return CallAnalytics()
