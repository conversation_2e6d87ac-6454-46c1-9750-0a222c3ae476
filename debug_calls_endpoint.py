#!/usr/bin/env python3
"""
Debug script to check the /api/v1/transcription/calls endpoint issue.
This script will help identify why the endpoint returns empty items.
"""

import sys
import os
sys.path.append('.')

from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.models import CallData, Transcription
from app.services.db_service import get_all_call_data, count_call_data
from app.models.filters import CallFilters, FilterType

def debug_database_content():
    """Debug the database content to understand why calls endpoint returns empty items."""
    
    print("🔍 Debugging /api/v1/transcription/calls endpoint...")
    print("=" * 60)
    
    # Get database session
    db = next(get_db())
    
    try:
        # 1. Check total count of CallData records
        total_call_data = db.query(CallData).count()
        print(f"📊 Total CallData records in database: {total_call_data}")
        
        # 2. Check total count of Transcription records
        total_transcriptions = db.query(Transcription).count()
        print(f"📊 Total Transcription records in database: {total_transcriptions}")
        
        # 3. Check CallData records with transcription_id
        call_data_with_transcription = db.query(CallData).filter(
            CallData.transcription_id.isnot(None)
        ).count()
        print(f"📊 CallData records with transcription_id: {call_data_with_transcription}")
        
        # 4. Check CallData records without transcription_id
        call_data_without_transcription = db.query(CallData).filter(
            CallData.transcription_id.is_(None)
        ).count()
        print(f"📊 CallData records without transcription_id: {call_data_without_transcription}")
        
        print("\n" + "=" * 60)
        
        # 5. Test the actual service function with no filters
        print("🧪 Testing get_all_call_data service function...")
        
        # Test with no filters
        calls_no_filter = get_all_call_data(db, skip=0, limit=10, filters=None)
        print(f"📋 get_all_call_data (no filters) returned: {len(calls_no_filter)} items")
        
        # Test count function with no filters
        count_no_filter = count_call_data(db, filters=None)
        print(f"📋 count_call_data (no filters) returned: {count_no_filter}")
        
        # 6. Test with default filters (FilterType.ALL)
        default_filters = CallFilters(filter_type=FilterType.ALL)
        calls_default_filter = get_all_call_data(db, skip=0, limit=10, filters=default_filters)
        print(f"📋 get_all_call_data (default filters) returned: {len(calls_default_filter)} items")
        
        count_default_filter = count_call_data(db, filters=default_filters)
        print(f"📋 count_call_data (default filters) returned: {count_default_filter}")
        
        print("\n" + "=" * 60)
        
        # 7. Show sample CallData records
        print("📝 Sample CallData records:")
        sample_calls = db.query(CallData).limit(3).all()
        
        for i, call in enumerate(sample_calls, 1):
            print(f"\n  Record {i}:")
            print(f"    ID: {call.id}")
            print(f"    Call ID: {call.call_id}")
            print(f"    Agent Name: {call.agent_name}")
            print(f"    Call Date: {call.call_date}")
            print(f"    Transcription ID: {call.transcription_id}")
            print(f"    Processed: {call.processed}")
            print(f"    Created At: {call.created_at}")
        
        # 8. Test the join query directly
        print("\n" + "=" * 60)
        print("🔗 Testing join query directly...")
        
        join_query = db.query(CallData).outerjoin(
            Transcription, CallData.transcription_id == Transcription.id
        )
        
        join_results = join_query.limit(5).all()
        print(f"📋 Direct join query returned: {len(join_results)} records")
        
        for i, call in enumerate(join_results, 1):
            print(f"\n  Join Result {i}:")
            print(f"    Call ID: {call.call_id}")
            print(f"    Agent Name: {call.agent_name}")
            print(f"    Has Transcription: {call.transcription is not None}")
            if call.transcription:
                print(f"    Transcription ID: {call.transcription.id}")
        
        print("\n" + "=" * 60)
        print("✅ Debug complete!")
        
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    debug_database_content()
