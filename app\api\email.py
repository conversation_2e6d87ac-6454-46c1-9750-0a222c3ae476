"""
Email API Module

This module provides production-ready email API endpoints for configuration
and status monitoring. Manual email sending endpoints have been removed for
production security and operational consistency.

Email sending is handled automatically through:
- Automated email reporting system (via cron jobs)
- System notifications and alerts

For manual email testing or troubleshooting, use the cron job manual trigger
endpoint in the cron-jobs API module.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from app.services.auth_service import get_current_active_user
from app.db.models import User
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/config-status")
async def check_email_configuration(
    current_user: User = Depends(get_current_active_user)
):
    """
    Check if email configuration is properly set up.

    This endpoint allows authenticated users to verify if the email service
    is properly configured with valid SMTP credentials.

    Args:
        current_user: The authenticated user making the request

    Returns:
        dict: Configuration status information
    """
    try:
        from app.services.email_service import validate_email_configuration

        is_configured = validate_email_configuration()

        return {
            "configured": is_configured,
            "message": "Email service is properly configured" if is_configured else "Email service configuration is missing or invalid",
            "smtp_email_set": bool(getattr(__import__('app.core.settings', fromlist=['settings']).settings, 'SMTP_EMAIL', None))
        }

    except Exception as e:
        logger.error(f"Error checking email configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error checking email configuration"
        )
