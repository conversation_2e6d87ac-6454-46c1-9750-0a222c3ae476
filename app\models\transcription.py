from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from enum import Enum


class TranscriptionOptions(BaseModel):
    """Model for transcription options."""

    smart_format: bool = Field(True, description="Improve readability with additional formatting")
    punctuate: bool = Field(True, description="Add punctuation and capitalization")
    paragraphs: bool = Field(True, description="Split audio into paragraphs")
    diarize: bool = Field(False, description="Recognize speaker changes")
    profanity_filter: bool = Field(False, description="Remove profanity")
    analyze_sentiment: bool = Field(False, description="Analyze sentiment of the conversation")
    description: Optional[str] = Field(None, description="Optional description of the audio content")


class SpeakerSegment(BaseModel):
    """Model for speaker segments in diarized transcription."""

    speaker: str = Field(..., description="Speaker identifier (e.g., 'spk_0', 'spk_1')")
    text: str = Field(..., description="Text spoken by this speaker")


class SentimentLevel(str, Enum):
    """Sentiment level enum."""
    VERY_NEGATIVE = "very_negative"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    POSITIVE = "positive"
    VERY_POSITIVE = "very_positive"


class ConversationRole(str, Enum):
    """Role in the conversation."""
    STUDENT = "student"
    UNIVERSITY_ASSISTANT = "university_assistant"
    OTHER = "other"
    UNKNOWN = "unknown"


class SentimentAnalysis(BaseModel):
    """Model for sentiment analysis results."""

    is_student_university_conversation: bool = Field(
        False,
        description="Whether the conversation appears to be between a student and university assistant"
    )

    overall_sentiment: SentimentLevel = Field(
        SentimentLevel.NEUTRAL,
        description="Overall sentiment of the conversation"
    )

    relevant_category: Optional[str] = Field(
        None,
        description="The most relevant school information category for this conversation"
    )

    key_points: List[str] = Field(
        default_factory=list,
        description="Key points extracted from the conversation"
    )

    speaker_roles: Dict[str, ConversationRole] = Field(
        default_factory=dict,
        description="Mapping of speaker IDs to their roles in the conversation"
    )

    summary: str = Field(
        "",
        description="Brief summary of the conversation"
    )

    action_items: List[str] = Field(
        default_factory=list,
        description="Suggested action items based on the conversation"
    )


class InformationAccuracy(BaseModel):
    """Model for information accuracy analysis."""

    missed_information: List[str] = Field(
        default_factory=list,
        description="Information that was available in school details but not mentioned by staff"
    )
    incorrect_information: List[Dict[str, str]] = Field(
        default_factory=list,
        description="Information that was incorrectly provided by staff"
    )
    incomplete_information: List[Dict[str, str]] = Field(
        default_factory=list,
        description="Information that was incompletely provided by staff"
    )
    correct_information: List[str] = Field(
        default_factory=list,
        description="Information that was correctly provided by staff"
    )
    overall_assessment: str = Field(
        "",
        description="Overall assessment of information accuracy"
    )
    accuracy_percentage: Union[float, str] = Field(
        0.0,
        description="Percentage of accuracy in the overall call (0-100) or 'NA' if not applicable"
    )
    conversation_flags: Dict[str, bool] = Field(
        default_factory=lambda: {"satisfaction": False, "confusion": False, "urgency": False, "frustration": False, "abusive": False},
        description="Flags indicating presence of satisfaction, confusion, urgency, frustration, and abusive behavior in the conversation"
    )
    conversation_tone: Optional[str] = Field(
        None,
        description="Overall tone of the conversation (pleasant, dull, etc.)"
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="When the analysis was created"
    )


class TranscriptionResponse(BaseModel):
    """Model for transcription response."""

    id: str = Field(..., description="Unique identifier for the transcription")
    text: str = Field(..., description="Transcribed text")
    duration_seconds: float = Field(0.0, description="Duration of the audio in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata from the transcription")
    created_at: datetime = Field(default_factory=datetime.now, description="When the transcription was created")
    speaker_segments: List[SpeakerSegment] = Field(
        default_factory=list,
        description="Segments of text by different speakers (when diarization is enabled)"
    )
    sentiment_analysis: Optional[SentimentAnalysis] = Field(
        None,
        description="Sentiment analysis of the conversation (when sentiment analysis is enabled)"
    )
    information_accuracy: Optional[InformationAccuracy] = Field(
        None,
        description="Information accuracy analysis comparing staff statements against school details"
    )
