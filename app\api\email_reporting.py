"""
AI-Powered Email Reporting API Module

This module provides the API router for AI-powered email reporting functionality.
Email reports are automatically generated and sent through the cron job system
after daily call processing completes. No manual endpoints are exposed for
production security and operational consistency.

The automated AI email reporting system:
- Integrates with the daily call processing cron job
- Sends personalized, AI-generated performance feedback emails to agents
- Uses OpenAI GPT-4 Mini for intelligent email content generation
- Includes appreciative and constructive feedback based on call data
- Uses Gmail SMTP with app password authentication
- Provides comprehensive error handling and fallback templates
- No temporary files or cleanup required

For manual testing or troubleshooting, use the cron job manual trigger endpoint
in the cron-jobs API module.
"""

from fastapi import APIRouter
import logging

logger = logging.getLogger(__name__)

# Router for potential future production email reporting endpoints
router = APIRouter()

# Note: Email reporting is handled automatically by the cron job system.
# See app.services.email_reporting_service.EmailReportingService for the core functionality.
# See app.api.cron_jobs for manual trigger capabilities (admin only).
