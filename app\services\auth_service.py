from sqlalchemy.orm import Session
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTP<PERSON>earer, HTTPAuthorizationCredentials
from jose import JW<PERSON>rror, jwt
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from app.db.models import User
from app.models.user import TokenData, UserLogin
from app.core.security import verify_password, create_access_token, SECRET_KEY, ALGORITHM
from app.db.database import get_db
from app.core.settings import settings

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login", auto_error=False)

# HTTP Bearer scheme for token authentication
security = HTTPBearer(auto_error=False)


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """
    Get a user by email.

    Args:
        db: Database session
        email: User email

    Returns:
        Optional[User]: User if found, None otherwise
    """
    return db.query(User).filter(User.email == email).first()


def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """
    Authenticate a user.

    Args:
        db: Database session
        email: User email
        password: User password

    Returns:
        Optional[User]: User if authentication successful, None otherwise
    """
    user = get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def get_token_from_authorization(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    token: str = Depends(oauth2_scheme)
) -> str:
    """
    Extract token from either HTTPBearer or OAuth2PasswordBearer.

    Args:
        credentials: HTTP Authorization credentials
        token: OAuth2 token

    Returns:
        str: The extracted token

    Raises:
        HTTPException: If no valid token is found
    """
    if credentials:
        return credentials.credentials
    elif token:
        return token

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )


def get_current_user(token: str = Depends(get_token_from_authorization), db: Session = Depends(get_db)) -> User:
    """
    Get the current authenticated user.

    Args:
        token: JWT token
        db: Database session

    Returns:
        User: The authenticated user

    Raises:
        HTTPException: If authentication fails
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = TokenData(email=email)
    except JWTError:
        raise credentials_exception

    user = get_user_by_email(db, email=token_data.email)
    if user is None:
        raise credentials_exception
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")

    return user


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get the current active user.

    Args:
        current_user: The current authenticated user

    Returns:
        User: The current active user

    Raises:
        HTTPException: If the user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


def create_admin_user(db: Session) -> None:
    """
    Create an admin user if it doesn't exist.

    Args:
        db: Database session
    """
    from app.core.security import get_password_hash

    # Check if admin user already exists
    admin_email = settings.ADMIN_EMAIL
    admin_user = get_user_by_email(db, admin_email)

    if not admin_user:
        # Create admin user
        from app.db.models import User

        admin_user = User(
            email=admin_email,
            hashed_password=get_password_hash(settings.ADMIN_PASSWORD),
            is_active=True,
            is_admin=True
        )

        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
