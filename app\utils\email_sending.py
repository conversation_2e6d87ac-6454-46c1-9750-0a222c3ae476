import smtplib
import ssl
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import os

class SimpleEmailSender:
    def __init__(self, email, password):
        self.email = email
        self.password = password
        self.smtp_server, self.smtp_port = self._get_smtp_settings()

    def _get_smtp_settings(self):
        """Gmail SMTP settings"""
        return ('smtp.gmail.com', 587)

    def send_simple_email(self, to_email, subject, message):
        """Send simple email without attachment"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email
            msg['To'] = to_email
            msg['Subject'] = subject

            # Add message body (detect HTML vs plain text)
            # Handle markdown-wrapped HTML content
            cleaned_message = message.strip()
            if cleaned_message.startswith('```html'):
                # Remove markdown wrapper
                lines = cleaned_message.split('\n')
                if lines[0].strip() == '```html' and lines[-1].strip() == '```':
                    cleaned_message = '\n'.join(lines[1:-1])

            message_lower = cleaned_message.lower().strip()
            is_html = (message_lower.startswith('<!doctype html') or
                      message_lower.startswith('<html') or
                      '<html>' in message_lower or
                      '<html ' in message_lower)

            if is_html:
                msg.attach(MIMEText(cleaned_message, 'html'))
            else:
                msg.attach(MIMEText(cleaned_message, 'plain'))

            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email, self.password)
                server.send_message(msg)

            print(f"✅ Email sent successfully to {to_email}")
            return True

        except Exception as e:
            print(f"❌ Error sending email to {to_email}: {str(e)}")
            return False

    def send_email_with_excel(self, to_email, subject, message, excel_file_path):
        """Send email with Excel attachment"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email
            msg['To'] = to_email
            msg['Subject'] = subject

            # Add message body (detect HTML vs plain text)
            # Handle markdown-wrapped HTML content
            cleaned_message = message.strip()
            if cleaned_message.startswith('```html'):
                # Remove markdown wrapper
                lines = cleaned_message.split('\n')
                if lines[0].strip() == '```html' and lines[-1].strip() == '```':
                    cleaned_message = '\n'.join(lines[1:-1])

            message_lower = cleaned_message.lower().strip()
            is_html = (message_lower.startswith('<!doctype html') or
                      message_lower.startswith('<html') or
                      '<html>' in message_lower or
                      '<html ' in message_lower)

            if is_html:
                msg.attach(MIMEText(cleaned_message, 'html'))
            else:
                msg.attach(MIMEText(cleaned_message, 'plain'))

            # Add Excel attachment
            if os.path.exists(excel_file_path):
                with open(excel_file_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())

                encoders.encode_base64(part)
                filename = os.path.basename(excel_file_path)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {filename}'
                )
                msg.attach(part)

                print(f"Excel file '{filename}' attached successfully")
            else:
                print(f"Error: Excel file not found at {excel_file_path}")
                return False

            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email, self.password)
                server.send_message(msg)

            print(f"✅ Email sent successfully to {to_email}")
            return True

        except Exception as e:
            print(f"❌ Error sending email to {to_email}: {str(e)}")
            return False
