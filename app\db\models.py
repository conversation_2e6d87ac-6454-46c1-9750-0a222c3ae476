from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Float, <PERSON><PERSON><PERSON>, Text, JSO<PERSON>, DateTime, ForeignKey, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

Base = declarative_base()


# Enum definitions for better storage
class SentimentEnum(enum.Enum):
    """Enum for sentiment values."""
    VERY_NEGATIVE = "very_negative"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    POSITIVE = "positive"
    VERY_POSITIVE = "very_positive"


class ConversationFlagEnum(enum.Enum):
    """Enum for conversation flags."""
    FRUSTRATION = "frustration"
    CONFUSION = "confusion"
    URGENCY = "urgency"
    SATISFACTION = "satisfaction"
    ABUSIVE = "abusive"


class ConversationToneEnum(enum.Enum):
    """Enum for conversation tone analysis."""
    PROFESSIONAL = "professional"
    WARM = "warm"
    ENTHUSIASTIC = "enthusiastic"
    NEUTRAL = "neutral"
    DISMISSIVE = "dismissive"
    IMPATIENT = "impatient"


def generate_uuid():
    """Generate a UUID for database records."""
    return str(uuid.uuid4())


class User(Base):
    """Model for storing user data."""

    __tablename__ = "users"

    id = Column(String, primary_key=True, default=generate_uuid)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)


class Transcription(Base):
    """Model for storing transcription data."""

    __tablename__ = "transcriptions"

    id = Column(String, primary_key=True, default=generate_uuid)
    filename = Column(String, nullable=False, index=True)
    text = Column(Text, nullable=False)
    transcription_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

    # Relationship with speaker segments
    speaker_segments = relationship("SpeakerSegmentDB", back_populates="transcription", cascade="all, delete-orphan")

    # Relationship with sentiment analysis
    sentiment_analysis = relationship("SentimentAnalysisDB", back_populates="transcription",
                                     uselist=False, cascade="all, delete-orphan")

    # Relationship with conversation flags
    conversation_flags = relationship("ConversationFlagDB", back_populates="transcription", cascade="all, delete-orphan")

    # Relationship with conversation tone
    conversation_tone = relationship("ConversationToneDB", back_populates="transcription",
                                   uselist=False, cascade="all, delete-orphan")

    # Relationship with information accuracy
    information_accuracy = relationship("InformationAccuracyDB", back_populates="transcription",
                                       uselist=False, cascade="all, delete-orphan")


class SpeakerSegmentDB(Base):
    """Model for storing speaker segments."""

    __tablename__ = "speaker_segments"

    id = Column(String, primary_key=True, default=generate_uuid)
    transcription_id = Column(String, ForeignKey("transcriptions.id", ondelete="CASCADE"), nullable=False)
    speaker = Column(String, nullable=False)
    text = Column(Text, nullable=False)

    # Relationship with transcription
    transcription = relationship("Transcription", back_populates="speaker_segments")


class SentimentAnalysisDB(Base):
    """Model for storing sentiment analysis results."""

    __tablename__ = "sentiment_analysis"

    id = Column(String, primary_key=True, default=generate_uuid)
    transcription_id = Column(String, ForeignKey("transcriptions.id", ondelete="CASCADE"), nullable=False, unique=True)
    is_student_university_conversation = Column(Boolean, default=False)
    overall_sentiment = Column(Enum(SentimentEnum), nullable=True, index=True)  # Use enum with index for better filtering
    relevant_category = Column(String, nullable=True, index=True)
    key_points = Column(JSON, nullable=True)
    speaker_roles = Column(JSON, nullable=True)
    summary = Column(Text, nullable=True)
    action_items = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

    # Relationship with transcription
    transcription = relationship("Transcription", back_populates="sentiment_analysis")


class ConversationFlagDB(Base):
    """Model for storing conversation flags separately for better filtering."""

    __tablename__ = "conversation_flags"

    id = Column(String, primary_key=True, default=generate_uuid)
    transcription_id = Column(String, ForeignKey("transcriptions.id", ondelete="CASCADE"), nullable=False, index=True)
    flag_type = Column(Enum(ConversationFlagEnum), nullable=False, index=True)  # Use enum with index for better filtering
    is_present = Column(Boolean, default=False, nullable=False, index=True)  # Whether this flag is present
    created_at = Column(DateTime, default=datetime.now)

    # Relationship with transcription
    transcription = relationship("Transcription", back_populates="conversation_flags")


class ConversationToneDB(Base):
    """Model for storing conversation tone analysis."""

    __tablename__ = "conversation_tones"

    id = Column(String, primary_key=True, default=generate_uuid)
    transcription_id = Column(String, ForeignKey("transcriptions.id", ondelete="CASCADE"), nullable=False, unique=True)
    tone = Column(Enum(ConversationToneEnum), nullable=True, index=True)  # Use enum with index for better filtering
    created_at = Column(DateTime, default=datetime.now)

    # Relationship with transcription
    transcription = relationship("Transcription", back_populates="conversation_tone")


class InformationAccuracyDB(Base):
    """Model for storing information accuracy analysis."""

    __tablename__ = "information_accuracy"

    id = Column(String, primary_key=True, default=generate_uuid)
    transcription_id = Column(String, ForeignKey("transcriptions.id", ondelete="CASCADE"), nullable=False, unique=True)
    missed_information = Column(JSON, nullable=True)
    incorrect_information = Column(JSON, nullable=True)
    incomplete_information = Column(JSON, nullable=True)
    correct_information = Column(JSON, nullable=True)
    overall_assessment = Column(Text, nullable=True)
    accuracy_percentage = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

    # Relationship with transcription
    transcription = relationship("Transcription", back_populates="information_accuracy")


class CallData(Base):
    """Model for storing call data from MCube API."""

    __tablename__ = "call_data"

    id = Column(String, primary_key=True, default=generate_uuid)
    call_id = Column(String, nullable=False, index=True, unique=True)  # Ensure call_id is unique
    audio_url = Column(String, nullable=True)
    call_duration = Column(Integer, default=0)
    transcription_id = Column(String, ForeignKey("transcriptions.id", ondelete="SET NULL"), nullable=True)
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)

    # Additional fields from MCube API
    agent_name = Column(String, nullable=True)
    call_date = Column(String, nullable=True)
    call_time = Column(String, nullable=True)
    group_name = Column(String, nullable=True)
    student_id = Column(String, nullable=True)
    student_name = Column(String, nullable=True)
    call_direction = Column(String, nullable=True)
    call_source = Column(String, nullable=True)

    # Relationship with transcription
    transcription = relationship("Transcription")


class TranscriptionFailure(Base):
    """Model for storing failed transcription attempts."""

    __tablename__ = "transcription_failures"

    id = Column(String, primary_key=True, default=generate_uuid)
    call_id = Column(String, nullable=False, index=True)
    start_date = Column(String, nullable=True)
    end_date = Column(String, nullable=True)
    error_message = Column(Text, nullable=False)
    audio_url = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now)


class SchoolInformation(Base):
    """Model for storing school information by category."""

    __tablename__ = "school_information"

    id = Column(String, primary_key=True, default=generate_uuid)
    category = Column(String, nullable=False, index=True, unique=True)  # Unique constraint on category
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class EmailContact(Base):
    """Model for storing email contacts."""

    __tablename__ = "email_contacts"

    id = Column(String, primary_key=True, default=generate_uuid)
    name = Column(String, nullable=False, index=True, unique=True)  # Unique constraint on name
    email = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class CronJobExecutionDB(Base):
    """Model for storing cron job execution history."""

    __tablename__ = "cron_job_executions"

    id = Column(String, primary_key=True, default=generate_uuid)
    job_type = Column(String, nullable=False, index=True)
    status = Column(String, nullable=False, index=True)
    start_time = Column(DateTime, default=datetime.now, nullable=False, index=True)
    end_time = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)

    # Job-specific data
    date_range_start = Column(String, nullable=True)
    date_range_end = Column(String, nullable=True)
    calls_processed = Column(Integer, nullable=True)
    calls_found = Column(Integer, nullable=True)
    max_concurrent = Column(Integer, nullable=True)

    # Error information
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)

    # Metadata
    triggered_by = Column(String, default="cron", nullable=False)
    job_metadata = Column(JSON, nullable=True)
