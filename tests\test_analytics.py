import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from app.main import app
from app.models.analytics import CallAnalytics, SentimentDistribution, EmotionalFlagCounts

client = TestClient(app)


@patch("app.services.analytics_service.get_call_analytics")
def test_get_analytics_endpoint(mock_get_analytics):
    """Test the analytics endpoint."""
    # Configure the mock to return a sample analytics object
    mock_analytics = CallAnalytics(
        total_calls=10,
        average_duration=120.5,
        average_accuracy=85.3,
        positive_sentiment_percentage=33.3,
        sentiment_distribution=SentimentDistribution(
            positive_count=3,
            negative_count=3,
            neutral_count=4,
            positive_percentage=30.0,
            negative_percentage=30.0,
            neutral_percentage=40.0
        ),
        emotional_flags=EmotionalFlagCounts(
            frustration=4,
            confusion=3,
            urgency=2,
            satisfaction=4
        )
    )
    mock_get_analytics.return_value = mock_analytics
    
    # Create a mock token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************.KPKzrPU5GVyYSgYmY3lXWiC4MrZrT_LjIch034HgKM8"
    
    # Make the request with the token
    response = client.get(
        "/api/v1/transcription/analytics",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    
    # Verify the response data
    assert data["total_calls"] == 10
    assert data["average_duration"] == 120.5
    assert data["average_accuracy"] == 85.3
    assert data["positive_sentiment_percentage"] == 33.3
    
    # Verify sentiment distribution
    assert data["sentiment_distribution"]["positive_count"] == 3
    assert data["sentiment_distribution"]["negative_count"] == 3
    assert data["sentiment_distribution"]["neutral_count"] == 4
    assert data["sentiment_distribution"]["positive_percentage"] == 30.0
    assert data["sentiment_distribution"]["negative_percentage"] == 30.0
    assert data["sentiment_distribution"]["neutral_percentage"] == 40.0
    
    # Verify emotional flags
    assert data["emotional_flags"]["frustration"] == 4
    assert data["emotional_flags"]["confusion"] == 3
    assert data["emotional_flags"]["urgency"] == 2
    assert data["emotional_flags"]["satisfaction"] == 4
    
    # Verify the mock was called
    mock_get_analytics.assert_called_once()


@patch("app.services.analytics_service.get_call_analytics")
def test_get_analytics_endpoint_unauthorized(mock_get_analytics):
    """Test the analytics endpoint without authentication."""
    # Make the request without a token
    response = client.get("/api/v1/transcription/analytics")
    
    # Check that the response is unauthorized
    assert response.status_code == 401
    
    # Verify the mock was not called
    mock_get_analytics.assert_not_called()
