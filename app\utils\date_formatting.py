"""
Date Formatting Utilities

This module provides utilities for formatting dates in various formats,
particularly for email headers and user-friendly display.
"""

from datetime import datetime, date
from typing import Union, Optional
import logging

logger = logging.getLogger(__name__)


def format_date_general(date_input: Union[datetime, date, str], include_time: bool = False) -> str:
    """
    Format a date in general format (e.g., '29 June 2025').
    
    Args:
        date_input: Date to format (datetime, date, or ISO string)
        include_time: Whether to include time in the format
        
    Returns:
        str: Formatted date string (e.g., '29 June 2025' or '29 June 2025 at 14:30')
        
    Examples:
        >>> format_date_general(datetime(2025, 6, 29))
        '29 June 2025'
        >>> format_date_general(datetime(2025, 6, 29, 14, 30), include_time=True)
        '29 June 2025 at 14:30'
        >>> format_date_general('2025-06-29')
        '29 June 2025'
    """
    try:
        # Convert input to datetime object
        if isinstance(date_input, str):
            # Try to parse ISO format string
            if 'T' in date_input or ' ' in date_input:
                # Full datetime string
                dt = datetime.fromisoformat(date_input.replace('Z', '+00:00'))
            else:
                # Date only string
                dt = datetime.strptime(date_input, '%Y-%m-%d')
        elif isinstance(date_input, date) and not isinstance(date_input, datetime):
            # Convert date to datetime
            dt = datetime.combine(date_input, datetime.min.time())
        elif isinstance(date_input, datetime):
            dt = date_input
        else:
            raise ValueError(f"Unsupported date type: {type(date_input)}")
        
        # Format the date
        day = dt.day
        month = dt.strftime('%B')  # Full month name
        year = dt.year
        
        if include_time and isinstance(date_input, datetime):
            time_str = dt.strftime('%H:%M')
            return f"{day} {month} {year} at {time_str}"
        else:
            return f"{day} {month} {year}"
            
    except Exception as e:
        logger.error(f"Error formatting date {date_input}: {str(e)}")
        # Fallback to string representation
        return str(date_input)


def format_date_range_general(
    start_date: Union[datetime, date, str], 
    end_date: Union[datetime, date, str],
    include_time: bool = False
) -> str:
    """
    Format a date range in general format.
    
    Args:
        start_date: Start date to format
        end_date: End date to format
        include_time: Whether to include time in the format
        
    Returns:
        str: Formatted date range string
        
    Examples:
        >>> format_date_range_general('2025-06-29', '2025-06-30')
        '29 June 2025 to 30 June 2025'
        >>> format_date_range_general('2025-06-29', '2025-06-29')
        '29 June 2025'
    """
    try:
        start_formatted = format_date_general(start_date, include_time)
        end_formatted = format_date_general(end_date, include_time)
        
        # If same date, return single date
        if start_formatted == end_formatted:
            return start_formatted
        
        return f"{start_formatted} to {end_formatted}"
        
    except Exception as e:
        logger.error(f"Error formatting date range {start_date} to {end_date}: {str(e)}")
        return f"{start_date} to {end_date}"


def format_datetime_for_email_header(dt: Union[datetime, str]) -> str:
    """
    Format datetime specifically for email headers in general format.
    
    Args:
        dt: Datetime to format
        
    Returns:
        str: Formatted datetime for email header
        
    Examples:
        >>> format_datetime_for_email_header(datetime(2025, 6, 29, 14, 30))
        '29 June 2025'
    """
    return format_date_general(dt, include_time=False)


def format_current_date_general() -> str:
    """
    Format the current date in general format.
    
    Returns:
        str: Current date in general format
        
    Examples:
        >>> format_current_date_general()  # If today is 2025-06-29
        '29 June 2025'
    """
    return format_date_general(datetime.now())


def format_current_datetime_general() -> str:
    """
    Format the current datetime in general format with time.
    
    Returns:
        str: Current datetime in general format with time
        
    Examples:
        >>> format_current_datetime_general()  # If now is 2025-06-29 14:30
        '29 June 2025 at 14:30'
    """
    return format_date_general(datetime.now(), include_time=True)


def parse_iso_to_general(iso_date_str: str) -> str:
    """
    Convert ISO date string to general format.
    
    Args:
        iso_date_str: ISO format date string (e.g., '2025-06-29' or '2025-06-29T14:30:00')
        
    Returns:
        str: Date in general format
        
    Examples:
        >>> parse_iso_to_general('2025-06-29')
        '29 June 2025'
        >>> parse_iso_to_general('2025-06-29T14:30:00')
        '29 June 2025'
    """
    return format_date_general(iso_date_str)


def get_month_name(month_number: int) -> str:
    """
    Get full month name from month number.
    
    Args:
        month_number: Month number (1-12)
        
    Returns:
        str: Full month name
        
    Examples:
        >>> get_month_name(6)
        'June'
        >>> get_month_name(12)
        'December'
    """
    try:
        if not 1 <= month_number <= 12:
            raise ValueError(f"Month number must be between 1 and 12, got {month_number}")
        
        # Create a dummy date with the given month
        dummy_date = datetime(2000, month_number, 1)
        return dummy_date.strftime('%B')
        
    except Exception as e:
        logger.error(f"Error getting month name for {month_number}: {str(e)}")
        return f"Month{month_number}"


def format_report_period(start_date: str, end_date: str) -> str:
    """
    Format a report period for display in emails and reports.
    
    Args:
        start_date: Start date in ISO format
        end_date: End date in ISO format
        
    Returns:
        str: Formatted report period
        
    Examples:
        >>> format_report_period('2025-06-29', '2025-06-30')
        '29 June 2025 to 30 June 2025'
    """
    return format_date_range_general(start_date, end_date)
