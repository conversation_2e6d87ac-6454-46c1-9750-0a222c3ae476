import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock
from app.main import app
from app.models.email import Em<PERSON>Re<PERSON>, EmailWithAttachmentRequest

client = TestClient(app)


@pytest.fixture
def mock_email_sender():
    """Mock the SimpleEmailSender class."""
    with patch('app.services.email_service.SimpleEmailSender') as mock:
        mock_instance = MagicMock()
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_settings():
    """Mock the settings with email configuration."""
    with patch('app.services.email_service.settings') as mock:
        mock.SMTP_EMAIL = "<EMAIL>"
        mock.SMTP_PASSWORD = "test_password"
        yield mock


@pytest.fixture
def auth_headers():
    """Get authentication headers for testing."""
    # First login to get token
    login_response = client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "12345"}
    )
    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    return {}


def test_send_simple_email_success(mock_email_sender, mock_settings, auth_headers):
    """Test successful simple email sending."""
    # Mock successful email sending
    mock_email_sender.send_simple_email.return_value = True
    
    email_data = {
        "to_email": "<EMAIL>",
        "subject": "Test Subject",
        "message": "Test message content"
    }
    
    response = client.post(
        "/api/v1/email/send",
        json=email_data,
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["message"] == "Email sent successfully"
        assert response_data["recipient"] == "<EMAIL>"


def test_send_email_without_auth():
    """Test that email sending requires authentication."""
    email_data = {
        "to_email": "<EMAIL>",
        "subject": "Test Subject",
        "message": "Test message content"
    }
    
    response = client.post("/api/v1/email/send", json=email_data)
    assert response.status_code == 401


def test_send_email_with_attachment_success(mock_email_sender, mock_settings, auth_headers):
    """Test successful email with attachment sending."""
    # Mock successful email sending and file existence
    mock_email_sender.send_email_with_excel.return_value = True
    
    with patch('os.path.exists', return_value=True):
        email_data = {
            "to_email": "<EMAIL>",
            "subject": "Test Subject with Attachment",
            "message": "Test message with attachment",
            "excel_file_path": "/path/to/test.xlsx"
        }
        
        response = client.post(
            "/api/v1/email/send-with-attachment",
            json=email_data,
            headers=auth_headers
        )
        
        if auth_headers:  # Only test if authentication is available
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["success"] is True
            assert response_data["message"] == "Email with attachment sent successfully"


def test_check_email_configuration(auth_headers):
    """Test email configuration status endpoint."""
    response = client.get(
        "/api/v1/email/config-status",
        headers=auth_headers
    )
    
    if auth_headers:  # Only test if authentication is available
        assert response.status_code == 200
        response_data = response.json()
        assert "configured" in response_data
        assert "message" in response_data
        assert "smtp_email_set" in response_data


def test_invalid_email_format():
    """Test validation of email format."""
    email_data = {
        "to_email": "invalid-email",
        "subject": "Test Subject",
        "message": "Test message content"
    }
    
    response = client.post("/api/v1/email/send", json=email_data)
    assert response.status_code == 422  # Validation error


def test_missing_required_fields():
    """Test validation of required fields."""
    email_data = {
        "to_email": "<EMAIL>",
        # Missing subject and message
    }
    
    response = client.post("/api/v1/email/send", json=email_data)
    assert response.status_code == 422  # Validation error
